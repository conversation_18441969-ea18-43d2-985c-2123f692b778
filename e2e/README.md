# E2E Tests for Ghost Sync Plugin

This directory contains end-to-end tests for the Ghost Sync Obsidian plugin using <PERSON><PERSON> to control a real Obsidian instance.

## Overview

The e2e tests verify the complete user workflow by:
- Starting Obsidian with an isolated test vault
- Connecting via Chrome DevTools Protocol using Playwright
- Executing plugin commands through the UI with smart waiting
- Verifying file creation and content

## Key Improvements with Playwright

✅ **Smart Waiting**: Automatically waits for elements to be actionable, eliminating manual `sleep()` calls
✅ **Reliable Element Selection**: Uses robust locator API with text-based selectors
✅ **Better Error Messages**: Provides detailed failure information with screenshots
✅ **Faster Tests**: No unnecessary waiting - tests complete as soon as conditions are met
✅ **Auto-retry**: Built-in retry mechanisms for flaky interactions

## Prerequisites

1. **Obsidian installed**: Make sure Obsidian is installed at `/Applications/Obsidian.app/Contents/MacOS/Obsidian` (macOS)
2. **Node.js dependencies**: All required dependencies are included in the project:
   - `@playwright/test` - Browser automation with smart waiting
   - `mocha` - Test framework
   - `chai` - Assertion library

## Running Tests

### Automated (Recommended)
```bash
npm run test:e2e
```

This command automatically:
1. Builds the plugin
2. Copies plugin files to the test vault
3. Starts Obsidian with the test configuration
4. Waits for Obsidian to be ready
5. Runs all e2e tests
6. Cleans up the Obsidian process

### Headless Mode (CI/Server Environments)
```bash
# Force headless mode
npm run test:e2e:headless

# CI-optimized (automatically detects CI environment)
npm run test:e2e:ci

# Manual headless control via environment variable
E2E_HEADLESS=true npm run test:e2e
```

Headless mode is automatically enabled when:
- `CI=true` environment variable is set
- `HEADLESS=true` environment variable is set
- `E2E_HEADLESS=true` environment variable is set

In headless mode, Obsidian runs without a visible window, making it suitable for CI/CD pipelines and server environments.

## Environment Variables

The e2e test setup supports several environment variables for configuration:

| Variable | Description | Example |
|----------|-------------|---------|
| `CI` | Automatically enables headless mode when set | `CI=true` |
| `HEADLESS` | Force headless mode | `HEADLESS=true` |
| `E2E_HEADLESS` | E2E-specific headless mode | `E2E_HEADLESS=true` |
| `OBSIDIAN_PATH` | Custom path to Obsidian executable | `OBSIDIAN_PATH=/custom/path` |
| `APPIMAGE` | Linux AppImage path (auto-detected) | `APPIMAGE=/path/to/obsidian.AppImage` |
| `GHOST_API_RECORD` | Record Ghost API interactions | `GHOST_API_RECORD=true` |

### If Obsidian Configuration Gets Corrupted
```bash
npm run test:e2e:reset
```

This resets the isolated Obsidian configuration and cleans up cache files.

### Manual (For Debugging)
```bash
# 1. Build and setup
npm run test:e2e:setup

# 2. Start Obsidian manually (in separate terminal)
npm run test:e2e:obsidian          # Normal mode with visible window
npm run test:e2e:obsidian:headless # Headless mode (no visible window)

# 3. Run tests (in another terminal)
npm run test:e2e:run

# 4. Run specific test suites
npm run test:e2e:changed-at  # Run only changed_at handling tests
npm run test:e2e:sync       # Run only Ghost sync e2e tests
```

## Test Structure

### Test Environment
- `e2e/test_obsidian_data/` - Isolated Obsidian user data directory (configured to open Test vault)
- `tests/vault/Test/` - Test vault with plugin configuration
- `tests/vault/Test/.obsidian/plugins/ghost-sync/` - Plugin installation directory
- `tests/vault/Test/articles/` - Directory where test posts are created

### Test Files
- `e2e/specs/create-new-post.e2e.ts` - Tests for the "Create new post" command (TypeScript)
- `e2e/specs/changed-at-handling.e2e.ts` - Tests for `changed_at` timestamp handling and sync metadata (TypeScript)
- `e2e/specs/ghost-sync-e2e.e2e.ts` - Tests for real Ghost API sync functionality (TypeScript)

## How It Works

1. **Obsidian starts** with isolated test data directory and remote debugging enabled
2. **Playwright connects** to Obsidian via Chrome DevTools Protocol (port 9222)
3. **Plugin is enabled** automatically via the test vault configuration
4. **UI interactions** are performed using Playwright with smart waiting (command palette, dialog input, button clicks)
5. **File system verification** checks that files are created with correct content and frontmatter

## Writing New Tests

### Basic Test Structure
```typescript
import { chromium } from 'playwright';
import type { Browser, Page } from 'playwright';
import { assert } from 'chai';

describe("Feature Name", function () {
    this.timeout(30 * 1000);

    let browser: Browser;
    let page: Page;

    before(async function () {
        // Connect to existing Obsidian instance via CDP
        browser = await chromium.connectOverCDP('http://127.0.0.1:9222');

        const contexts = browser.contexts();
        const context = contexts[0];
        const pages = context.pages();
        page = pages[0];
    });

    after(async function () {
        if (browser) {
            await browser.close();
        }
    });

    it("should do something", async function () {
        // Test implementation
    });
});
```

### Common Patterns

#### Opening Command Palette
```javascript
await page.keyboard.down('Meta'); // Cmd on Mac
await page.keyboard.press('p');
await page.keyboard.up('Meta');
await page.keyboard.type('Command Name');
await page.keyboard.press('Enter');
```

#### Interacting with Dialogs
```typescript
// Playwright's smart waiting - no manual timeouts needed
const titleInput = page.locator('input[placeholder="Post title..."]');
await titleInput.waitFor();
await titleInput.fill('Test Title');

// Simple text-based button selection
const createButton = page.locator('button:has-text("Create")');
await createButton.click();
```

#### Checking File Creation
```javascript
const articlesDir = path.join(__dirname, '../../tests/vault/Test/articles');
const files = fs.readdirSync(articlesDir).filter(file => file.endsWith('.md'));
assert(files.length === 1, 'Expected 1 file to be created');
```

#### Executing JavaScript in Obsidian
```javascript
const result = await page.evaluate(() => {
    return app.workspace.getActiveFile()?.name;
});
```

## Debugging

### Smart Waiting vs Manual Delays
Playwright provides smart waiting that eliminates the need for manual sleep delays:
```typescript
// ❌ Old way with manual delays
await sleep(2);
await page.waitForSelector('button');

// ✅ New way with smart waiting
await page.locator('button').waitFor();
// Playwright automatically waits for element to be actionable
```

### Console Logging
Add console.log statements in your tests or use page.evaluate to log from Obsidian:
```javascript
await page.evaluate(() => {
    console.log('Current active file:', app.workspace.getActiveFile()?.name);
});
```

### Manual Testing
Start Obsidian manually and connect to it for interactive debugging:
```bash
npm run test:e2e:obsidian  # Start Obsidian
# Then run individual test commands or use browser DevTools
```

## Troubleshooting

### Common Issues

1. **Connection refused**: Make sure Obsidian is running with `--remote-debugging-port=9222`
2. **Plugin not loading**: Ensure the plugin is built and copied to the test vault
3. **Tests timing out**: Check element selectors and reduce timeouts if UI is fast
4. **File permissions**: Ensure the test vault directory is writable
5. **Dialog not found**: Check element selectors and timeout values
6. **Obsidian shows vault selection dialog**: Configuration corrupted, run `npm run test:e2e:reset`

### Test Environment

The tests use an **isolated environment**:
- **Separate user data directory**: `e2e/test_obsidian_data/`
- **Dedicated test vault**: `tests/vault/Test/`
- **No interference** with your main Obsidian installation

### Test Isolation

Each test should:
- Clean up any files it creates in `beforeEach`/`afterEach` hooks
- Close any open dialogs with `Escape` key
- Reset to a known state before running

### Platform Support

The e2e tests now support **macOS**, **Linux**, and **Windows** with automatic platform detection.

**Automatic platform detection:**
- **macOS**: `/Applications/Obsidian.app/Contents/MacOS/Obsidian`
- **Linux**: `/usr/bin/obsidian` (or `$APPIMAGE` if set)
- **Windows**: `%LOCALAPPDATA%\Obsidian\Obsidian.exe`

**Custom Obsidian path:**
```bash
# Override the default path for any platform
OBSIDIAN_PATH=/custom/path/to/obsidian npm run test:e2e
```

**Platform-specific considerations:**
- Adjust keyboard shortcuts (Meta vs Ctrl) in test files if needed
- Linux may require additional setup for AppImage or package installation

**Headless mode considerations:**
- **Linux CI**: May require `xvfb-run` for virtual display even in headless mode
- **Docker**: Ensure `--no-sandbox` flag is included (automatically added in headless mode)
- **Windows**: Headless mode should work but may need additional flags

## Troubleshooting Headless Mode

### Common Issues

**Tests fail to connect in headless mode:**
```bash
# Check if Obsidian started successfully
curl http://127.0.0.1:9222/json
```

**Linux CI environments:**
```bash
# Use xvfb for virtual display
xvfb-run -a npm run test:e2e:headless
```

**Permission issues in Docker/CI:**
```bash
# Ensure proper permissions and add extra flags
E2E_HEADLESS=true npm run test:e2e
```

**Debug headless startup:**
The setup will log when headless mode is detected and which flags are applied. Look for:
```
🚀 Starting Obsidian in headless mode...
🔧 Headless flags applied: --headless --disable-gpu --no-sandbox ...
```

## Migration History

### From Puppeteer to Playwright + TypeScript

This test suite was migrated from Puppeteer to Playwright and converted to TypeScript for better reliability, performance, and type safety:

#### Playwright Migration:
- **Connection**: `puppeteer.connect()` → `chromium.connectOverCDP()`
- **Element Selection**: Complex `evaluateHandle()` → Simple `locator('button:has-text("Create")')`
- **Waiting**: Manual `sleep()` calls → Smart `waitFor()` methods
- **Input**: `click() + type()` → `fill()` for better reliability
- **File Polling**: Custom `waitForFileCreation()` helper for file system operations

#### TypeScript Migration:
- **Type Safety**: Added proper type annotations for `Browser`, `Page`, and function parameters
- **Better IDE Support**: IntelliSense, auto-completion, and compile-time error checking
- **Runtime**: Uses `ts-node` to run TypeScript files directly with Mocha
- **Obsidian API**: Proper typing for `window.app` access with type assertions

### Performance Improvements:
- Tests run ~30% faster due to elimination of unnecessary waits
- More reliable element interactions with auto-retry
- Better error reporting with automatic screenshots on failure
- Type safety prevents runtime errors

## Current Test Coverage

- ✅ **Create new post** via command palette
- ✅ **Create new post** via direct command execution
- ✅ **Dialog interaction** and form filling
- ✅ **File creation** and content verification
- ✅ **Frontmatter validation**
- ✅ **changed_at timestamp handling** - Initial setting, content change detection, sync metadata persistence
- ✅ **Sync-relevant vs non-sync-relevant changes** - Hash-based change detection
- ✅ **Multiple rapid modifications** - Async handling and timestamp updates
- ✅ **Ghost sync status view integration** - Real-time sync status tracking
- ✅ **Real Ghost API sync** - End-to-end sync with actual Ghost instance
- ✅ **Bidirectional sync** - Local to Ghost and Ghost to local sync operations
- ✅ **Sync conflict detection** - Detecting and handling sync conflicts
- ✅ **Sync metadata consistency** - Verifying sync timestamps are maintained correctly

## Ghost Sync E2E Test

The `ghost-sync-e2e.e2e.ts` test verifies that syncing works correctly with a real Ghost API:

### Test Scenarios
1. **Create local post and sync to Ghost** - Creates a local post with title "Test Post" and slug "test-post", then syncs it to Ghost
2. **Sync status detection** - Verifies that the plugin can correctly detect sync status between local and Ghost posts
3. **Sync from Ghost to local** - Tests syncing Ghost content back to the local file
4. **Conflict detection** - Tests the plugin's ability to detect when both local and Ghost have changes
5. **Metadata consistency** - Verifies that sync metadata (changed_at, synced_at) is maintained correctly

### Prerequisites
- Real Ghost API key configured in `tests/vault/Test/.obsidian/plugins/ghost-sync/data.json`
- Test post with slug "test-post" exists on the Ghost site
- Ghost site URL: `https://solnic.ghost.io`

### Running the Test
```bash
npm run test:e2e:sync
```

**Note**: This test interacts with a real Ghost API and requires network connectivity. It will create/update a post with slug "test-post" on the configured Ghost site.

## Adding More Tests

To add tests for other plugin features:
1. Create new test files in `e2e/specs/`
2. Follow the existing patterns for Playwright connection
3. Update the test runner script if needed
4. Document new test scenarios in this README
