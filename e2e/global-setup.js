#!/usr/bin/env node

const { spawn, exec } = require('child_process');
const path = require('path');

// Platform-specific Obsidian paths
function getObsidianPath() {
  const platform = process.platform;
  const customPath = process.env.OBSIDIAN_PATH;

  if (customPath) {
    return customPath;
  }

  switch (platform) {
    case 'darwin':
      return '/Applications/Obsidian.app/Contents/MacOS/Obsidian';
    case 'linux':
      return process.env.APPIMAGE || '/usr/bin/obsidian';
    case 'win32':
      return process.env.LOCALAPPDATA + '\\Obsidian\\Obsidian.exe';
    default:
      throw new Error(`Unsupported platform: ${platform}. Set OBSIDIAN_PATH environment variable.`);
  }
}

const OBSIDIAN_PATH = getObsidianPath();
const USER_DATA_DIR = path.join(process.cwd(), 'e2e/test_obsidian_data');
const VAULT_PATH = path.join(process.cwd(), 'tests/vault/Test');
const PRISTINE_VAULT_PATH = path.join(process.cwd(), 'tests/vault/Test.pristine');
const DEBUG_PORT = 9222;
const HEALTH_CHECK_URL = `http://127.0.0.1:${DEBUG_PORT}/json`;

// Detect if we should run in headless mode (default: true, disable with E2E_HEADLESS=false)
const isHeadless = process.env.E2E_HEADLESS !== 'false';

// Headless-specific flags for Electron/Obsidian
const HEADLESS_FLAGS = [
  '--no-sandbox',
  '--disable-gpu',
  '--disable-dev-shm-usage',
  '--disable-setuid-sandbox',
  '--disable-background-timer-throttling',
  '--disable-backgrounding-occluded-windows',
  '--disable-renderer-backgrounding',
  '--disable-features=TranslateUI',
  '--disable-ipc-flooding-protection',
  '--disable-extensions',
  '--disable-plugins',
  '--disable-web-security',
  '--hide-scrollbars',
  '--mute-audio',
  // Electron-specific flags to hide window on macOS
  '--show=false',
  '--enable-transparent-visuals',
  '--disable-transparency'
];

let obsidianProcess = null;

async function sleep(seconds) {
  return new Promise(resolve => setTimeout(resolve, seconds * 1000));
}

async function checkObsidianReady() {
  try {
    const response = await fetch(HEALTH_CHECK_URL);
    const data = await response.json();
    return Array.isArray(data) && data.length > 0;
  } catch (error) {
    return false;
  }
}

async function waitForObsidian(maxWaitSeconds = 30) {
  for (let i = 0; i < maxWaitSeconds; i++) {
    if (await checkObsidianReady()) {
      // If in headless mode on macOS, minimize the window
      if (isHeadless && process.platform === 'darwin') {
        await minimizeObsidianWindow();
      }

      return true;
    }
    await sleep(1);
  }

  return false;
}

async function minimizeObsidianWindow() {
  try {
    const appleScript = `
      tell application "System Events"
        tell process "Obsidian"
          set visible to false
          try
            set miniaturized of windows to true
          end try
        end tell
      end tell
    `;

    const { exec } = require('child_process');
    await new Promise((resolve) => {
      exec(`osascript -e '${appleScript}'`, () => {
        resolve(); // Always resolve, don't fail the test if minimizing fails
      });
    });

    // Give it a moment to minimize
    await sleep(1);

  } catch (error) {
    // Silently ignore errors
  }
}

async function waitForObsidianApp(maxWaitSeconds = 30) {
  const { chromium } = require('playwright');

  for (let i = 0; i < maxWaitSeconds; i++) {
    try {
      const browser = await chromium.connectOverCDP('http://127.0.0.1:9222');
      const contexts = browser.contexts();

      if (contexts.length > 0) {
        const context = contexts[0];
        const pages = context.pages();

        if (pages.length > 0) {
          const page = pages[0];

          // Check if app.plugins is available
          const appReady = await page.evaluate(() => {
            return typeof window.app !== 'undefined' &&
              typeof window.app.plugins !== 'undefined';
          });

          await browser.close();

          if (appReady) {
            return true;
          }
        } else {
          await browser.close();
        }
      } else {
        await browser.close();
      }
    } catch (error) {
      // Connection failed, continue waiting
    }

    await sleep(1);
  }

  return false;
}

function buildPlugin() {
  return new Promise((resolve, reject) => {
    exec('npm run build', (error) => {
      if (error) {
        reject(error);
      } else {
        resolve();
      }
    });
  });
}

function restoreVaultFromPristine() {
  return new Promise((resolve, reject) => {
    exec(`rm -rf "${VAULT_PATH}" && cp -r "${PRISTINE_VAULT_PATH}" "${VAULT_PATH}"`, (error) => {
      if (error) {
        reject(error);
      } else {
        resolve();
      }
    });
  });
}

function copyPluginFiles() {
  return new Promise((resolve, reject) => {
    exec('cp main.js manifest.json styles.css tests/vault/Test/.obsidian/plugins/ghost-sync/', (error) => {
      if (error) {
        reject(error);
      } else {
        resolve();
      }
    });
  });
}

function startObsidian() {
  return new Promise((resolve, reject) => {
    // Base arguments for Obsidian
    const baseArgs = [
      `--user-data-dir=${USER_DATA_DIR}`,
      `--remote-debugging-port=${DEBUG_PORT}`,
      VAULT_PATH
    ];

    // Add headless flags if needed
    const args = isHeadless ? [...baseArgs, ...HEADLESS_FLAGS] : baseArgs;

    // On macOS in headless mode, try to start the process in background
    const spawnOptions = {
      stdio: 'pipe',
      detached: false
    };

    // Add environment variables for headless mode on macOS
    if (isHeadless && process.platform === 'darwin') {
      spawnOptions.env = {
        ...process.env,
        // Try to prevent window from appearing
        'ELECTRON_ENABLE_LOGGING': '0',
        'ELECTRON_DISABLE_SECURITY_WARNINGS': '1'
      };
    }

    obsidianProcess = spawn(OBSIDIAN_PATH, args, spawnOptions);

    obsidianProcess.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('DevTools listening')) {
        resolve();
      }
    });

    obsidianProcess.stderr.on('data', () => {
      // Ignore stderr for now as Obsidian outputs some warnings
    });

    obsidianProcess.on('error', (error) => {
      reject(error);
    });

    obsidianProcess.on('exit', () => {
      // Silent exit handling
    });

    // Fallback: resolve after 3 seconds even if we don't see the DevTools message
    setTimeout(() => {
      resolve();
    }, 3000);
  });
}

function cleanup() {
  if (obsidianProcess) {
    obsidianProcess.kill('SIGTERM');

    // Force kill after 5 seconds if it doesn't exit gracefully
    setTimeout(() => {
      if (obsidianProcess && !obsidianProcess.killed) {
        obsidianProcess.kill('SIGKILL');
      }
    }, 5000);
  }
}

// Global setup function called by Vitest
export async function setup() {
  try {
    // Setup
    await buildPlugin();
    await restoreVaultFromPristine();
    await copyPluginFiles();

    // Check if Obsidian is already running
    const alreadyRunning = await checkObsidianReady();

    if (!alreadyRunning) {
      // Start Obsidian only if it's not already running
      await startObsidian();

      // Wait for Obsidian to be ready
      const isReady = await waitForObsidian();
      if (!isReady) {
        throw new Error('Obsidian failed to start');
      }
    }

    // Wait for app object to be ready (whether we started it or it was already running)
    const appReady = await waitForObsidianApp();
    if (!appReady) {
      throw new Error('Obsidian app object failed to initialize');
    }

  } catch (error) {
    // Only cleanup if we started the process
    if (obsidianProcess) {
      cleanup();
    }
    throw error;
  }
}

// Global teardown function called by Vitest
export async function teardown() {
  // Only cleanup if we started the Obsidian process
  if (obsidianProcess) {
    cleanup();
    // Give some time for cleanup
    await sleep(2);
  }
}

// Handle process termination
process.on('SIGINT', () => {
  cleanup();
  process.exit(0);
});

process.on('SIGTERM', () => {
  cleanup();
  process.exit(0);
});
