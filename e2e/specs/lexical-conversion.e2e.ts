import { chromium } from 'playwright';
import type { <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { expect } from 'vitest';
import { setupGhostAPIMocking, shouldRecordGhostAPI } from '../fixtures/ghost-api-helper';
import { verifyPluginAvailable, waitForAsyncOperation } from '../helpers/plugin-setup';
import { registerPageForUIReset } from '../helpers/test-setup';

/**
 * E2E Tests for Lexical-Markdown Conversion in Sync Operations
 *
 * These tests verify that the Lexical parser correctly converts content
 * during sync operations between Obsidian and Ghost.
 */

describe("Lexical Conversion E2E Tests", () => {
  let browser: Browser;
  let page: Page;

  beforeAll(async () => {
    // Connect to existing Obsidian instance via CDP
    browser = await chromium.connectOverCDP('http://127.0.0.1:9222');

    // Get the first page (Obsidian window)
    const contexts = browser.contexts();
    const context = contexts[0];
    const pages = context.pages();
    page = pages[0];

    console.log("Connected to Obsidian via Playwright");

    // Set up Ghost API mocking
    await setupGhostAPIMocking(page, context, {
      ghostUrl: 'https://solnic.ghost.io',
      scenario: 'lexical-conversion',
      record: shouldRecordGhostAPI()
    });

    // Enable the ghost-sync plugin
    await page.evaluate(() => {
      (window as any).app.plugins.setEnable(true);
      (window as any).app.plugins.enablePlugin('ghost-sync');
    });

    await waitForAsyncOperation(500);
    await verifyPluginAvailable(page);
    registerPageForUIReset(page);
  });

  beforeEach(async () => {
    // Clean up any open dialogs or modals before each test
    await page.evaluate(() => {
      const modals = document.querySelectorAll('.modal, .modal-container, .publish-dialog, [data-modal]');
      modals.forEach(modal => {
        const closeButton = modal.querySelector('.modal-close, .close, [aria-label="Close"], button[data-action="close"]');
        if (closeButton) {
          (closeButton as HTMLElement).click();
        } else {
          modal.remove();
        }
      });

      const notices = document.querySelectorAll('.notice');
      notices.forEach(notice => notice.remove());

      document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape', bubbles: true }));
    });

    await waitForAsyncOperation(500);
  });

  afterEach(async () => {
    // Clean up test files
    await page.evaluate(async () => {
      const app = (window as any).app;
      const testFiles = [
        'articles/lexical-test-basic.md',
        'articles/lexical-test-formatting.md',
        'articles/lexical-test-complex.md',
        'articles/lexical-test-callouts.md'
      ];

      for (const filePath of testFiles) {
        try {
          const file = app.vault.getAbstractFileByPath(filePath);
          if (file) {
            await app.vault.delete(file);
          }
        } catch (error) {
          console.log('Error cleaning up file:', filePath, error);
        }
      }
    });

    await waitForAsyncOperation(1000);
  });

  afterAll(async () => {
    if (browser) {
      await browser.close();
    }
  });

  test("should convert basic markdown to lexical format", async () => {
    console.log("Testing basic markdown to lexical conversion");

    const testContent = `---
title: "Lexical Test - Basic"
slug: "lexical-test-basic"
status: draft
---

# Basic Markdown Test

This is a paragraph with **bold** and *italic* text.

## Subheading

Another paragraph with \`inline code\`.

- List item 1
- List item 2
- List item 3

[Link to example](https://example.com)`;

    // Create test file
    const filePath = 'articles/lexical-test-basic.md';
    await page.evaluate(async ({ path, content }) => {
      const app = (window as any).app;
      
      // Ensure articles directory exists
      const articlesDir = 'articles';
      const abstractFile = app.vault.getAbstractFileByPath(articlesDir);
      if (!abstractFile) {
        await app.vault.createFolder(articlesDir);
      }

      await app.vault.create(path, content);
    }, { path: filePath, content: testContent });

    await waitForAsyncOperation(1000);

    // Test that the lexical parser can convert the content
    const conversionResult = await page.evaluate(async ({ path }) => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath(path);
      
      if (!file) {
        return { success: false, error: 'File not found' };
      }

      const content = await app.vault.read(file);
      
      // Access the lexical parser through the plugin
      const plugin = app.plugins.plugins['ghost-sync'];
      if (!plugin || !plugin.lexicalParser) {
        return { success: false, error: 'Lexical parser not available' };
      }

      try {
        const result = plugin.lexicalParser.markdownToLexical(content);
        return {
          success: result.success,
          hasContent: result.success && result.data?.root?.children?.length > 0,
          nodeTypes: result.success ? result.data?.root?.children?.map((child: any) => child.type) : [],
          error: result.error
        };
      } catch (error) {
        return { success: false, error: error.message };
      }
    }, { path: filePath });

    expect(conversionResult.success).toBe(true);
    expect(conversionResult.hasContent).toBe(true);
    expect(conversionResult.nodeTypes).toContain('heading');
    expect(conversionResult.nodeTypes).toContain('paragraph');
    expect(conversionResult.nodeTypes).toContain('list');
  });

  test("should convert complex formatting to lexical", async () => {
    console.log("Testing complex formatting conversion");

    const testContent = `---
title: "Lexical Test - Formatting"
slug: "lexical-test-formatting"
status: draft
---

# Complex Formatting Test

## Text Formatting

This paragraph contains **bold**, *italic*, \`inline code\`, and ~~strikethrough~~ text.

Combined formatting: ***bold and italic***, **bold with \`code\`**.

## Code Blocks

\`\`\`javascript
function hello() {
  console.log("Hello, world!");
}
\`\`\`

## Tables

| Header 1 | Header 2 | Header 3 |
| --- | --- | --- |
| Cell 1 | **Bold** | *Italic* |
| Cell 4 | \`Code\` | [Link](https://example.com) |

## Images

![Test Image](https://example.com/image.jpg "Image Title")`;

    const filePath = 'articles/lexical-test-formatting.md';
    await page.evaluate(async ({ path, content }) => {
      const app = (window as any).app;
      await app.vault.create(path, content);
    }, { path: filePath, content: testContent });

    await waitForAsyncOperation(1000);

    const conversionResult = await page.evaluate(async ({ path }) => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath(path);
      const content = await app.vault.read(file);
      
      const plugin = app.plugins.plugins['ghost-sync'];
      const result = plugin.lexicalParser.markdownToLexical(content);
      
      return {
        success: result.success,
        nodeTypes: result.success ? result.data?.root?.children?.map((child: any) => child.type) : [],
        hasTable: result.success ? result.data?.root?.children?.some((child: any) => child.type === 'table') : false,
        hasCodeBlock: result.success ? result.data?.root?.children?.some((child: any) => child.type === 'codeblock') : false,
        hasImage: result.success ? result.data?.root?.children?.some((child: any) => 
          child.children?.some((grandchild: any) => grandchild.type === 'image')
        ) : false
      };
    }, { path: filePath });

    expect(conversionResult.success).toBe(true);
    expect(conversionResult.hasTable).toBe(true);
    expect(conversionResult.hasCodeBlock).toBe(true);
    expect(conversionResult.hasImage).toBe(true);
  });

  test("should convert Obsidian callouts to Ghost format", async () => {
    console.log("Testing callout conversion");

    const testContent = `---
title: "Lexical Test - Callouts"
slug: "lexical-test-callouts"
status: draft
---

# Callout Test

> [!note]
> This is a note callout with important information.

> [!tip]
> This is a helpful tip for users.

> [!warning]
> This is a warning about potential issues.

> [!danger]
> This is a danger callout for critical warnings.

Regular blockquote without callout syntax:

> This is just a regular blockquote
> that should be converted to a quote callout.`;

    const filePath = 'articles/lexical-test-callouts.md';
    await page.evaluate(async ({ path, content }) => {
      const app = (window as any).app;
      await app.vault.create(path, content);
    }, { path: filePath, content: testContent });

    await waitForAsyncOperation(1000);

    const conversionResult = await page.evaluate(async ({ path }) => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath(path);
      const content = await app.vault.read(file);
      
      const plugin = app.plugins.plugins['ghost-sync'];
      const result = plugin.lexicalParser.markdownToLexical(content);
      
      if (!result.success) {
        return { success: false, error: result.error };
      }

      const callouts = result.data?.root?.children?.filter((child: any) => child.type === 'callout') || [];
      const calloutTypes = callouts.map((callout: any) => callout.calloutType);
      
      return {
        success: true,
        calloutCount: callouts.length,
        calloutTypes: calloutTypes,
        hasNoteCallout: calloutTypes.includes('note'),
        hasTipCallout: calloutTypes.includes('tip'),
        hasWarningCallout: calloutTypes.includes('warning'),
        hasDangerCallout: calloutTypes.includes('danger'),
        hasQuoteCallout: calloutTypes.includes('quote')
      };
    }, { path: filePath });

    expect(conversionResult.success).toBe(true);
    expect(conversionResult.calloutCount).toBeGreaterThan(0);
    expect(conversionResult.hasNoteCallout).toBe(true);
    expect(conversionResult.hasTipCallout).toBe(true);
    expect(conversionResult.hasWarningCallout).toBe(true);
    expect(conversionResult.hasDangerCallout).toBe(true);
    expect(conversionResult.hasQuoteCallout).toBe(true);
  });

  test("should handle round-trip conversion without data loss", async () => {
    console.log("Testing round-trip conversion");

    const testContent = `---
title: "Lexical Test - Round-trip"
slug: "lexical-test-roundtrip"
status: draft
---

# Round-trip Test

This content should survive a round-trip conversion.

**Bold text** and *italic text* should be preserved.

\`\`\`javascript
// Code blocks should be preserved
console.log("Hello, world!");
\`\`\`

- List items
- Should be preserved
- In the same order

[Links](https://example.com) should work too.`;

    const filePath = 'articles/lexical-test-complex.md';
    await page.evaluate(async ({ path, content }) => {
      const app = (window as any).app;
      await app.vault.create(path, content);
    }, { path: filePath, content: testContent });

    await waitForAsyncOperation(1000);

    const roundTripResult = await page.evaluate(async ({ path }) => {
      const app = (window as any).app;
      const file = app.vault.getAbstractFileByPath(path);
      const originalContent = await app.vault.read(file);
      
      const plugin = app.plugins.plugins['ghost-sync'];
      
      // Convert to Lexical
      const lexicalResult = plugin.lexicalParser.markdownToLexical(originalContent);
      if (!lexicalResult.success) {
        return { success: false, error: 'Failed to convert to Lexical', stage: 'markdown-to-lexical' };
      }
      
      // Convert back to Markdown
      const markdownResult = plugin.lexicalParser.lexicalToMarkdown(lexicalResult.data);
      if (!markdownResult.success) {
        return { success: false, error: 'Failed to convert to Markdown', stage: 'lexical-to-markdown' };
      }
      
      return {
        success: true,
        originalLength: originalContent.length,
        convertedLength: markdownResult.data.length,
        preservedTitle: markdownResult.data.includes('# Round-trip Test'),
        preservedBold: markdownResult.data.includes('**Bold text**'),
        preservedItalic: markdownResult.data.includes('*italic text*'),
        preservedCodeBlock: markdownResult.data.includes('```javascript'),
        preservedList: markdownResult.data.includes('- List items'),
        preservedLink: markdownResult.data.includes('[Links](https://example.com)')
      };
    }, { path: filePath });

    expect(roundTripResult.success).toBe(true);
    expect(roundTripResult.preservedTitle).toBe(true);
    expect(roundTripResult.preservedBold).toBe(true);
    expect(roundTripResult.preservedItalic).toBe(true);
    expect(roundTripResult.preservedCodeBlock).toBe(true);
    expect(roundTripResult.preservedList).toBe(true);
    expect(roundTripResult.preservedLink).toBe(true);
  });
});
