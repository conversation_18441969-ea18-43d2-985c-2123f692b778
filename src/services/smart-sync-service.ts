import type { TFile } from "obsidian";
import type { GhostPost, LocalPost, SyncAnalysis, ArticleFrontMatter } from "../types";
import { SyncDecision } from "../types";
import type { ObsidianGhostAPI } from "../api/ghost-api";
import { ContentConverter } from "../utils/content-converter";
import type { SyncMetadataStorage } from "./sync-metadata-storage";

export interface SmartSyncServiceDependencies {
  ghostAPI: ObsidianGhostAPI;
  readFile: (file: TFile) => Promise<string>;
  writeFile: (file: TFile, content: string) => Promise<void>;
  parseMarkdown: (content: string) => { frontMatter: ArticleFrontMatter; content: string };
  syncMetadata: SyncMetadataStorage;
  renameFileForTitleChange?: (file: TFile, newTitle: string) => Promise<TFile | null>;
}

/**
 * Service for intelligent bidirectional sync between Obsidian and Ghost
 * Uses timestamps to determine sync direction and avoid conflicts
 */
export class SmartSyncService {
  private dependencies: SmartSyncServiceDependencies;

  constructor(dependencies: SmartSyncServiceDependencies) {
    this.dependencies = dependencies;
  }

  /**
   * Parse a local file into a LocalPost model
   */
  async parseLocalPost(file: TFile): Promise<LocalPost> {
    console.log('=== PARSING LOCAL POST ===');
    console.log('File path:', file.path);

    const content = await this.dependencies.readFile(file);
    const parsed = this.dependencies.parseMarkdown(content);

    console.log('Raw frontmatter:', JSON.stringify(parsed.frontMatter, null, 2));

    // Extract synced_at and changed_at from metadata storage (NOT frontmatter)
    const syncedAt = this.dependencies.syncMetadata.getSyncedAt(file);
    const changedAt = this.dependencies.syncMetadata.getChangedAt(file);

    console.log('Extracted timestamps:');
    console.log('  syncedAt:', syncedAt);
    console.log('  changedAt:', changedAt);
    console.log('  fileModifiedAt:', file.stat?.mtime ? new Date(file.stat.mtime).toISOString() : 'undefined');

    return {
      frontMatter: parsed.frontMatter,
      content: parsed.content,
      syncedAt: syncedAt,
      changedAt: changedAt,
      fileModifiedAt: file.stat?.mtime || 0,
      filePath: file.path
    };
  }

  /**
   * Find Ghost post for a local file using UUID-based tracking ONLY
   */
  async findGhostPost(file: TFile, localPost: LocalPost): Promise<GhostPost | null> {
    // Only use UUID-based lookup - no more slug-based fallbacks
    const uuid = this.dependencies.syncMetadata.getGhostUuid(file);
    if (uuid) {
      const post = await this.dependencies.ghostAPI.getPostById(uuid);
      if (post) {
        return post;
      }
    }

    // If no UUID or post not found, return null (new post)
    return null;
  }

  /**
   * Analyze what sync action should be taken
   */
  async analyzeSyncNeeded(localPost: LocalPost, ghostPost?: GhostPost): Promise<SyncAnalysis> {
    console.log('=== SYNC ANALYSIS START ===');
    console.log('Local post file path:', localPost.filePath);

    if (!ghostPost) {
      console.log('No Ghost post found - will sync TO Ghost');
      return {
        decision: SyncDecision.SYNC_TO_GHOST,
        localPost,
        reason: "Post doesn't exist in Ghost yet",
        localChangedTime: localPost.changedAt
      };
    }

    const ghostUpdatedTime = ghostPost.updated_at;
    const localChangedTime = localPost.changedAt;
    const lastSyncTime = localPost.syncedAt;

    console.log('SIMPLE SYNC LOGIC:');
    console.log('  ghostUpdatedTime (Ghost updated_at):', ghostUpdatedTime);
    console.log('  localChangedTime (changed_at):', localChangedTime);
    console.log('  lastSyncTime (synced_at):', lastSyncTime);

    // First sync scenario: no previous sync timestamp
    if (!lastSyncTime) {
      if (!localChangedTime || new Date(ghostUpdatedTime).getTime() > new Date(localChangedTime).getTime()) {
        console.log('DECISION: SYNC_FROM_GHOST (first sync - Ghost is newer or no local changes)');
        return {
          decision: SyncDecision.SYNC_FROM_GHOST,
          localPost,
          ghostPost,
          reason: "First sync - Ghost post is newer",
          ghostUpdatedTime,
          localChangedTime
        };
      } else {
        console.log('DECISION: SYNC_TO_GHOST (first sync - local is newer)');
        return {
          decision: SyncDecision.SYNC_TO_GHOST,
          localPost,
          ghostPost,
          reason: "First sync - local content is newer",
          ghostUpdatedTime,
          localChangedTime
        };
      }
    }

    // Check for conflict: ONLY if both Ghost AND local have changed since last sync
    const lastSyncTimeMs = new Date(lastSyncTime).getTime();
    const ghostChangedSinceSync = new Date(ghostUpdatedTime).getTime() > lastSyncTimeMs;
    const localChangedSinceSync = localChangedTime ? new Date(localChangedTime).getTime() > lastSyncTimeMs : false;

    console.log('Conflict check:');
    console.log('  ghostChangedSinceSync:', ghostChangedSinceSync);
    console.log('  localChangedSinceSync:', localChangedSinceSync);

    // CONFLICT: Both sides changed since last sync
    if (ghostChangedSinceSync && localChangedSinceSync) {
      console.log('DECISION: CONFLICT (both sides have changes since last sync)');
      return {
        decision: SyncDecision.CONFLICT,
        localPost,
        ghostPost,
        reason: "Both Ghost and local content have changes since last sync",
        lastSyncTime,
        ghostUpdatedTime,
        localChangedTime
      };
    }

    // NO CONFLICT: Check which side is newer or if no sync needed
    if (!ghostChangedSinceSync && !localChangedSinceSync) {
      console.log('DECISION: NO_SYNC_NEEDED (no changes since last sync)');
      return {
        decision: SyncDecision.NO_SYNC_NEEDED,
        localPost,
        ghostPost,
        reason: "No changes detected since last sync",
        ghostUpdatedTime,
        localChangedTime
      };
    }

    // Sync from whichever side has changes
    if (ghostChangedSinceSync) {
      console.log('DECISION: SYNC_FROM_GHOST (Ghost has changes)');
      return {
        decision: SyncDecision.SYNC_FROM_GHOST,
        localPost,
        ghostPost,
        reason: "Ghost has newer changes",
        lastSyncTime,
        ghostUpdatedTime,
        localChangedTime
      };
    } else {
      console.log('DECISION: SYNC_TO_GHOST (Local has changes)');
      return {
        decision: SyncDecision.SYNC_TO_GHOST,
        localPost,
        ghostPost,
        reason: "Local content has newer changes",
        lastSyncTime,
        ghostUpdatedTime,
        localChangedTime
      };
    }
  }

  /**
   * Sync local post to Ghost
   */
  async syncToGhost(localPost: LocalPost, file: TFile, existingGhostPost?: GhostPost | null): Promise<GhostPost> {
    const slug = localPost.frontMatter.slug || localPost.frontMatter.Slug;
    if (!slug) {
      throw new Error("Post must have a slug to sync to Ghost");
    }

    let existingPost: GhostPost | null = existingGhostPost || null;

    // Only do API lookups if no existing post was provided
    if (existingPost === undefined) {
      // Check if we have a UUID mapping for this file
      const existingUuid = this.dependencies.syncMetadata.getGhostUuid(file);

      if (existingUuid) {
        // Only use UUID-based lookup - no more slug-based fallbacks
        existingPost = await this.dependencies.ghostAPI.getPostById(existingUuid);
      }

      // If no UUID or post not found, existingPost remains null (new post)
    }

    // Create Ghost post data
    const postData = ContentConverter.createGhostPostData(
      localPost.frontMatter,
      localPost.content,
      {
        isUpdate: !!existingPost,
        existingPost: existingPost
      }
    );

    let result: GhostPost;
    if (existingPost) {
      result = await this.dependencies.ghostAPI.updatePost(postData);
    } else {
      result = await this.dependencies.ghostAPI.createPost(postData);
    }

    // Store UUID mapping for future syncs
    if (result.id) {
      await this.dependencies.syncMetadata.setGhostUuid(file, result.id);
    }

    return result;
  }

  /**
   * Sync from Ghost to local post
   */
  async syncFromGhost(file: TFile, ghostPost: GhostPost): Promise<TFile> {
    // Check if title changed and file needs to be renamed
    let targetFile = file;
    if (this.dependencies.renameFileForTitleChange) {
      const currentContent = await this.dependencies.readFile(file);
      const parsed = this.dependencies.parseMarkdown(currentContent);
      const currentTitle = parsed.frontMatter.title || parsed.frontMatter.Title;

      if (currentTitle && currentTitle !== ghostPost.title) {
        console.log(`Title changed from "${currentTitle}" to "${ghostPost.title}", renaming file`);
        const renamedFile = await this.dependencies.renameFileForTitleChange(file, ghostPost.title);
        if (renamedFile) {
          targetFile = renamedFile;
        }
      }
    }

    // Convert Ghost post to article content (no internal timestamps in frontmatter)
    const articleContent = ContentConverter.convertGhostPostToArticle(ghostPost);
    await this.dependencies.writeFile(targetFile, articleContent);

    // Store UUID mapping for future syncs
    if (ghostPost.id) {
      await this.dependencies.syncMetadata.setGhostUuid(targetFile, ghostPost.id);
    }

    // Set internal sync metadata using storage (NOT in frontmatter)
    await this.dependencies.syncMetadata.setSyncFromGhost(targetFile, ghostPost.updated_at, ghostPost);

    return targetFile;
  }

  /**
   * DEPRECATED: This method has been removed.
   * Use SyncMetadataStorage.setSyncedAt() instead to update timestamps in metadata storage.
   * Internal sync timestamps should NOT be stored in frontmatter.
   */
}
