/**
 * Converter for softbreak nodes between Markdown and Lexical formats
 */

import {
  NodeConverter,
  MarkdownNode,
  LexicalNode,
  ConversionContext,
} from '../types';

/**
 * Handles conversion of softbreak nodes (line breaks within paragraphs)
 */
export class SoftbreakConverter extends NodeConverter {
  /**
   * Check if this converter can handle the given node type
   */
  canHandle(nodeType: string): boolean {
    return nodeType === 'softbreak';
  }

  /**
   * Convert a Markdown softbreak node to Lexical format
   */
  markdownToLexical(
    node: MarkdownNode,
    context?: ConversionContext
  ): LexicalNode | LexicalNode[] {
    // Softbreaks are typically converted to line break nodes in Lexical
    return {
      type: 'linebreak',
      version: 1
    };
  }

  /**
   * Convert a Lexical linebreak node to Markdown format
   */
  lexicalToMarkdown(
    node: LexicalNode,
    context?: ConversionContext
  ): string {
    // Convert linebreak back to a soft line break (two spaces + newline)
    return '  \n';
  }
}
