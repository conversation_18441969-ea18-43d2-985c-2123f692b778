/**
 * Converter for code and code block nodes between Markdown and Lexical formats
 */

import {
  NodeConverter,
  MarkdownNode,
  LexicalNode,
  LexicalCodeNode,
  LexicalCodeBlockNode,
  MarkdownCodeNode,
  MarkdownCodeBlockNode,
  ConversionContext,
} from '../types';

/**
 * <PERSON><PERSON> conversion of code and code block nodes
 */
export class CodeConverter extends NodeConverter {
  /**
   * Check if this converter can handle the given node type
   */
  canHandle(nodeType: string): boolean {
    return ['code', 'codeblock', 'inlineCode'].includes(nodeType);
  }

  /**
   * Convert a Markdown code node to Lexical format
   */
  markdownToLexical(
    node: MarkdownNode,
    context?: ConversionContext
  ): LexicalNode | LexicalNode[] {
    if (node.type === 'code') {
      return this.convertCodeBlock(node as MarkdownCodeBlockNode);
    } else if (node.type === 'inlineCode') {
      return this.convertInlineCode(node as MarkdownCodeNode);
    }

    // Fallback
    return this.createLexicalCodeBlockNode(node.value || '', undefined);
  }

  /**
   * Convert a Lexical code node to Markdown format
   */
  lexicalToMarkdown(
    node: LexicalNode,
    context?: ConversionContext
  ): string {
    if (node.type === 'codeblock') {
      return this.convertLexicalCodeBlockToMarkdown(node as LexicalCodeBlockNode);
    } else if (node.type === 'code') {
      return this.convertLexicalInlineCodeToMarkdown(node as LexicalCodeNode);
    }

    return '';
  }

  /**
   * Convert Markdown code block to Lexical
   * @private
   */
  private convertCodeBlock(node: MarkdownCodeBlockNode): LexicalCodeBlockNode {
    return this.createLexicalCodeBlockNode(
      node.value || '',
      node.lang || undefined
    );
  }

  /**
   * Convert Markdown inline code to Lexical
   * @private
   */
  private convertInlineCode(node: MarkdownCodeNode): LexicalCodeNode {
    return this.createLexicalCodeNode(node.value || '');
  }

  /**
   * Convert Lexical code block to Markdown
   * @private
   */
  private convertLexicalCodeBlockToMarkdown(node: LexicalCodeBlockNode): string {
    const language = node.language || '';
    const code = node.code || '';

    // Use fenced code block syntax
    return `\`\`\`${language}\n${code}\n\`\`\``;
  }

  /**
   * Convert Lexical inline code to Markdown
   * @private
   */
  private convertLexicalInlineCodeToMarkdown(node: LexicalCodeNode): string {
    const code = node.code || '';
    
    // Handle backticks in code content
    if (code.includes('`')) {
      // Use double backticks if single backticks are present
      if (code.includes('``')) {
        // Use triple backticks if double backticks are present
        return `\`\`\`${code}\`\`\``;
      } else {
        return `\`\`${code}\`\``;
      }
    } else {
      return `\`${code}\``;
    }
  }

  /**
   * Create a Lexical code block node
   * @private
   */
  private createLexicalCodeBlockNode(
    code: string,
    language?: string
  ): LexicalCodeBlockNode {
    return {
      type: 'codeblock',
      code: this.normalizeCodeContent(code),
      language: this.normalizeLanguage(language),
      version: 1
    };
  }

  /**
   * Create a Lexical inline code node
   * @private
   */
  private createLexicalCodeNode(code: string): LexicalCodeNode {
    return {
      type: 'code',
      code: code.trim(),
      version: 1
    };
  }

  /**
   * Normalize code content
   * @private
   */
  private normalizeCodeContent(code: string): string {
    // Remove leading/trailing newlines but preserve internal formatting
    return code.replace(/^\n+|\n+$/g, '');
  }

  /**
   * Normalize language identifier
   * @private
   */
  private normalizeLanguage(language?: string): string | undefined {
    if (!language) {
      return undefined;
    }

    // Convert common language aliases to standard names
    const languageMap: Record<string, string> = {
      'js': 'javascript',
      'ts': 'typescript',
      'py': 'python',
      'rb': 'ruby',
      'sh': 'bash',
      'shell': 'bash',
      'yml': 'yaml',
      'md': 'markdown',
      'htm': 'html',
      'xml': 'markup',
      'c++': 'cpp',
      'c#': 'csharp',
      'cs': 'csharp',
      'f#': 'fsharp',
      'fs': 'fsharp',
      'vb': 'vbnet',
      'ps1': 'powershell',
      'psm1': 'powershell',
    };

    const normalized = language.toLowerCase().trim();
    return languageMap[normalized] || normalized;
  }

  /**
   * Parse fenced code blocks from markdown
   * @param text - Markdown text that may contain code blocks
   * @returns Array of parsed code block information
   */
  static parseFencedCodeBlocks(text: string): Array<{
    start: number;
    end: number;
    language?: string;
    code: string;
    fence: string;
  }> {
    const blocks: Array<{
      start: number;
      end: number;
      language?: string;
      code: string;
      fence: string;
    }> = [];

    // Regex for fenced code blocks with ``` or ~~~
    const fenceRegex = /^(```|~~~)([^\n]*)\n([\s\S]*?)\n\1$/gm;
    let match;

    while ((match = fenceRegex.exec(text)) !== null) {
      const [fullMatch, fence, languageInfo, code] = match;
      const language = languageInfo.trim() || undefined;

      blocks.push({
        start: match.index,
        end: match.index + fullMatch.length,
        language,
        code: code || '',
        fence
      });
    }

    return blocks;
  }

  /**
   * Parse indented code blocks from markdown
   * @param text - Markdown text that may contain indented code blocks
   * @returns Array of parsed code block information
   */
  static parseIndentedCodeBlocks(text: string): Array<{
    start: number;
    end: number;
    code: string;
  }> {
    const blocks: Array<{
      start: number;
      end: number;
      code: string;
    }> = [];

    const lines = text.split('\n');
    let currentBlock: { start: number; lines: string[] } | null = null;

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      const isCodeLine = /^    /.test(line) || (line.trim() === '' && currentBlock);

      if (isCodeLine && line.trim() !== '') {
        // Start or continue code block
        if (!currentBlock) {
          currentBlock = {
            start: text.indexOf(line),
            lines: []
          };
        }
        currentBlock.lines.push(line.substring(4)); // Remove 4-space indent
      } else if (currentBlock && line.trim() === '') {
        // Empty line in code block
        currentBlock.lines.push('');
      } else {
        // End of code block
        if (currentBlock && currentBlock.lines.length > 0) {
          const code = currentBlock.lines.join('\n').replace(/\n+$/, '');
          const endPos = currentBlock.start + code.length;

          blocks.push({
            start: currentBlock.start,
            end: endPos,
            code
          });
        }
        currentBlock = null;
      }
    }

    // Handle code block at end of text
    if (currentBlock && currentBlock.lines.length > 0) {
      const code = currentBlock.lines.join('\n').replace(/\n+$/, '');
      const endPos = currentBlock.start + code.length;

      blocks.push({
        start: currentBlock.start,
        end: endPos,
        code
      });
    }

    return blocks;
  }

  /**
   * Parse inline code from markdown
   * @param text - Text that may contain inline code
   * @returns Array of parsed inline code information
   */
  static parseInlineCode(text: string): Array<{
    start: number;
    end: number;
    code: string;
    fence: string;
  }> {
    const codes: Array<{
      start: number;
      end: number;
      code: string;
      fence: string;
    }> = [];

    // Handle different backtick patterns
    const patterns = [
      /```([^`]+)```/g,  // Triple backticks
      /``([^`]+)``/g,    // Double backticks
      /`([^`]+)`/g       // Single backticks
    ];

    for (const pattern of patterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        const [fullMatch, code] = match;
        const fence = fullMatch.substring(0, fullMatch.indexOf(code));

        codes.push({
          start: match.index,
          end: match.index + fullMatch.length,
          code: code.trim(),
          fence
        });
      }
    }

    // Sort by position and remove overlapping matches
    codes.sort((a, b) => a.start - b.start);
    
    const filtered: typeof codes = [];
    for (const code of codes) {
      const overlaps = filtered.some(existing => 
        (code.start >= existing.start && code.start < existing.end) ||
        (code.end > existing.start && code.end <= existing.end)
      );
      
      if (!overlaps) {
        filtered.push(code);
      }
    }

    return filtered;
  }

  /**
   * Escape code content for safe markdown output
   * @param code - Code content to escape
   * @returns Escaped code content
   */
  static escapeCodeContent(code: string): string {
    // No escaping needed for code blocks, but ensure proper line endings
    return code.replace(/\r\n/g, '\n').replace(/\r/g, '\n');
  }

  /**
   * Detect programming language from code content
   * @param code - Code content to analyze
   * @returns Detected language or undefined
   */
  static detectLanguage(code: string): string | undefined {
    const trimmed = code.trim();
    
    // Simple heuristics for common languages
    if (/^<\?php/.test(trimmed)) return 'php';
    if (/^#!/.test(trimmed) && /bash|sh/.test(trimmed)) return 'bash';
    if (/^#!/.test(trimmed) && /python/.test(trimmed)) return 'python';
    if (/^#!/.test(trimmed) && /node/.test(trimmed)) return 'javascript';
    if (/import\s+\w+\s+from/.test(trimmed)) return 'javascript';
    if (/from\s+\w+\s+import/.test(trimmed)) return 'python';
    if (/def\s+\w+\s*\(/.test(trimmed)) return 'python';
    if (/function\s+\w+\s*\(/.test(trimmed)) return 'javascript';
    if (/console\.log\s*\(/.test(trimmed)) return 'javascript';
    if (/print\s*\(/.test(trimmed)) return 'python';
    if (/<html|<div|<span/.test(trimmed)) return 'html';
    if (/\{[\s\S]*"[\w-]+"\s*:/.test(trimmed)) return 'json';
    if (/SELECT\s+.*FROM/i.test(trimmed)) return 'sql';

    return undefined;
  }
}
