/**
 * Converter for link nodes between Markdown and Lexical formats
 */

import {
  NodeConverter,
  MarkdownNode,
  LexicalNode,
  LexicalLinkNode,
  MarkdownLinkNode,
  ConversionContext,
} from '../types';
import { LexicalUtils } from '../utils/lexical-utils';
import { ConverterRegistry } from '../converter-registry';

/**
 * Handles conversion of link nodes
 */
export class LinkConverter extends NodeConverter {
  /**
   * Check if this converter can handle the given node type
   */
  canHandle(nodeType: string): boolean {
    return nodeType === 'link';
  }

  /**
   * Convert a Markdown link node to Lexical format
   */
  markdownToLexical(
    node: MarkdownNode,
    context?: ConversionContext
  ): LexicalNode | LexicalNode[] {
    const linkNode = node as MarkdownLinkNode;
    
    // Validate URL
    if (!linkNode.url) {
      console.warn('Link node missing URL, converting to text');
      return LexicalUtils.createTextNode(this.extractLinkText(linkNode));
    }

    // Convert child nodes (link text)
    const children: LexicalNode[] = [];
    
    if (linkNode.children) {
      for (const child of linkNode.children) {
        const converter = ConverterRegistry.getConverter(child.type);
        if (converter) {
          try {
            const converted = converter.markdownToLexical(child, context);
            if (Array.isArray(converted)) {
              children.push(...converted);
            } else if (converted) {
              children.push(converted);
            }
          } catch (error) {
            console.warn(`Error converting link child ${child.type}:`, error);
            // Add as text fallback
            if (child.value) {
              children.push(LexicalUtils.createTextNode(child.value));
            }
          }
        } else {
          // Fallback: convert to text if no converter found
          if (child.value) {
            children.push(LexicalUtils.createTextNode(child.value));
          } else if (child.type === 'text') {
            children.push(LexicalUtils.createTextNode(child.value || ''));
          }
        }
      }
    }

    // If no children, use URL as text
    if (children.length === 0) {
      children.push(LexicalUtils.createTextNode(linkNode.url));
    }

    // Merge adjacent text nodes
    const mergedChildren = LexicalUtils.mergeAdjacentTextNodes(children);

    return this.createLexicalLinkNode(
      linkNode.url,
      mergedChildren,
      linkNode.title
    );
  }

  /**
   * Convert a Lexical link node to Markdown format
   */
  lexicalToMarkdown(
    node: LexicalNode,
    context?: ConversionContext
  ): string {
    const linkNode = node as LexicalLinkNode;
    
    if (!linkNode.url) {
      // Fallback: extract text content
      return LexicalUtils.extractText(linkNode);
    }

    // Convert children to text
    const parts: string[] = [];

    if (linkNode.children) {
      for (const child of linkNode.children) {
        const converter = ConverterRegistry.getConverter(child.type);
        if (converter) {
          try {
            const converted = converter.lexicalToMarkdown(child, context);
            if (converted) {
              parts.push(converted);
            }
          } catch (error) {
            console.warn(`Error converting link child ${child.type}:`, error);
            // Fallback: extract text content
            const text = LexicalUtils.extractText(child);
            if (text) {
              parts.push(text);
            }
          }
        } else {
          // Fallback: extract text content
          const text = LexicalUtils.extractText(child);
          if (text) {
            parts.push(text);
          }
        }
      }
    }

    const linkText = parts.join('').trim();
    
    // If no link text, use URL
    const displayText = linkText || linkNode.url;

    // Format as markdown link
    if (linkNode.title) {
      return `[${displayText}](${linkNode.url} "${linkNode.title}")`;
    } else {
      return `[${displayText}](${linkNode.url})`;
    }
  }

  /**
   * Create a Lexical link node
   * @private
   */
  private createLexicalLinkNode(
    url: string,
    children: LexicalNode[],
    title?: string
  ): LexicalLinkNode {
    const linkNode: LexicalLinkNode = {
      type: 'link',
      url: this.normalizeURL(url),
      children,
      direction: 'ltr',
      format: '',
      indent: 0,
      version: 1
    };

    if (title) {
      linkNode.title = title;
    }

    // Set appropriate rel and target for external links
    if (this.isExternalURL(url)) {
      linkNode.rel = 'noreferrer';
      linkNode.target = '_blank';
    }

    return linkNode;
  }

  /**
   * Extract text content from a link node
   * @private
   */
  private extractLinkText(linkNode: MarkdownLinkNode): string {
    if (linkNode.children) {
      return linkNode.children
        .map(child => child.value || '')
        .join('')
        .trim();
    }
    return linkNode.url || '';
  }

  /**
   * Normalize URL format
   * @private
   */
  private normalizeURL(url: string): string {
    // Trim whitespace
    url = url.trim();

    // Handle relative URLs
    if (url.startsWith('/') || url.startsWith('./') || url.startsWith('../')) {
      return url;
    }

    // Handle anchor links
    if (url.startsWith('#')) {
      return url;
    }

    // Handle mailto links
    if (url.startsWith('mailto:')) {
      return url;
    }

    // Handle other protocols
    if (/^[a-z][a-z0-9+.-]*:/i.test(url)) {
      return url;
    }

    // Assume http if no protocol
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      return `https://${url}`;
    }

    return url;
  }

  /**
   * Check if URL is external
   * @private
   */
  private isExternalURL(url: string): boolean {
    // Consider external if it has a protocol and domain
    return /^https?:\/\//.test(url) && !this.isInternalDomain(url);
  }

  /**
   * Check if URL is to an internal domain
   * @private
   */
  private isInternalDomain(url: string): boolean {
    // This could be configured based on the site's domain
    // For now, assume all http/https URLs are external
    return false;
  }

  /**
   * Parse markdown link syntax
   * @param text - Text that may contain markdown links
   * @returns Array of parsed link information
   */
  static parseMarkdownLinks(text: string): Array<{
    start: number;
    end: number;
    text: string;
    url: string;
    title?: string;
  }> {
    const links: Array<{
      start: number;
      end: number;
      text: string;
      url: string;
      title?: string;
    }> = [];

    // Regex for markdown links: [text](url "title")
    const linkRegex = /\[([^\]]*)\]\(([^)]+?)(?:\s+"([^"]*)")?\)/g;
    let match;

    while ((match = linkRegex.exec(text)) !== null) {
      const [fullMatch, linkText, url, title] = match;
      links.push({
        start: match.index,
        end: match.index + fullMatch.length,
        text: linkText,
        url: url.trim(),
        title: title?.trim()
      });
    }

    return links;
  }

  /**
   * Parse reference-style markdown links
   * @param text - Text that may contain reference links
   * @param references - Map of reference definitions
   * @returns Array of parsed link information
   */
  static parseReferenceLinks(
    text: string,
    references: Map<string, { url: string; title?: string }>
  ): Array<{
    start: number;
    end: number;
    text: string;
    url: string;
    title?: string;
  }> {
    const links: Array<{
      start: number;
      end: number;
      text: string;
      url: string;
      title?: string;
    }> = [];

    // Regex for reference links: [text][ref] or [text][]
    const refLinkRegex = /\[([^\]]*)\]\[([^\]]*)\]/g;
    let match;

    while ((match = refLinkRegex.exec(text)) !== null) {
      const [fullMatch, linkText, refKey] = match;
      const key = refKey || linkText; // Use text as key if ref is empty
      const reference = references.get(key.toLowerCase());

      if (reference) {
        links.push({
          start: match.index,
          end: match.index + fullMatch.length,
          text: linkText,
          url: reference.url,
          title: reference.title
        });
      }
    }

    return links;
  }

  /**
   * Extract reference definitions from markdown
   * @param text - Markdown text
   * @returns Map of reference definitions
   */
  static extractReferenceDefinitions(text: string): Map<string, { url: string; title?: string }> {
    const references = new Map<string, { url: string; title?: string }>();

    // Regex for reference definitions: [key]: url "title"
    const refDefRegex = /^\s*\[([^\]]+)\]:\s*([^\s]+)(?:\s+"([^"]*)")?/gm;
    let match;

    while ((match = refDefRegex.exec(text)) !== null) {
      const [, key, url, title] = match;
      references.set(key.toLowerCase(), {
        url: url.trim(),
        title: title?.trim()
      });
    }

    return references;
  }
}
