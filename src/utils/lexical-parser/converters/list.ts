/**
 * Converter for list and list item nodes between Markdown and Lexical formats
 */

import {
  NodeConverter,
  MarkdownNode,
  LexicalNode,
  LexicalListNode,
  LexicalListItemNode,
  MarkdownListNode,
  MarkdownListItemNode,
  ConversionContext,
} from '../types';
import { LexicalUtils } from '../utils/lexical-utils';
import { ConverterRegistry } from '../converter-registry';

/**
 * Handles conversion of list and list item nodes
 */
export class ListConverter extends NodeConverter {
  /**
   * Check if this converter can handle the given node type
   */
  canHandle(nodeType: string): boolean {
    return ['list', 'listItem'].includes(nodeType);
  }

  /**
   * Convert a Markdown list node to Lexical format
   */
  markdownToLexical(
    node: MarkdownNode,
    context?: ConversionContext
  ): LexicalNode | LexicalNode[] {
    if (node.type === 'list') {
      return this.convertList(node as MarkdownListNode, context);
    } else if (node.type === 'listItem') {
      return this.convertListItem(node as MarkdownListItemNode, context);
    }

    return LexicalUtils.createTextNode('');
  }

  /**
   * Convert a Lexical list node to Markdown format
   */
  lexicalToMarkdown(
    node: LexicalNode,
    context?: ConversionContext
  ): string {
    if (node.type === 'list') {
      return this.convertLexicalListToMarkdown(node as LexicalListNode, context);
    } else if (node.type === 'listitem') {
      return this.convertLexicalListItemToMarkdown(node as LexicalListItemNode, context);
    }

    return '';
  }

  /**
   * Convert Markdown list to Lexical list
   * @private
   */
  private convertList(
    node: MarkdownListNode,
    context?: ConversionContext
  ): LexicalListNode {
    const listType = node.ordered ? 'number' : 'bullet';
    const children: LexicalListItemNode[] = [];

    if (node.children) {
      for (const child of node.children) {
        if (child.type === 'listItem') {
          const converted = this.convertListItem(child as MarkdownListItemNode, context);
          if (converted && converted.type === 'listitem') {
            children.push(converted as LexicalListItemNode);
          }
        }
      }
    }

    return LexicalUtils.createListNode(listType, children, node.start);
  }

  /**
   * Convert Markdown list item to Lexical list item
   * @private
   */
  private convertListItem(
    node: MarkdownListItemNode,
    context?: ConversionContext
  ): LexicalListItemNode {
    const children: LexicalNode[] = [];

    if (node.children) {
      for (const child of node.children) {
        const converter = ConverterRegistry.getConverter(child.type);
        if (converter) {
          try {
            const converted = converter.markdownToLexical(child, context);
            if (Array.isArray(converted)) {
              children.push(...converted);
            } else if (converted) {
              children.push(converted);
            }
          } catch (error) {
            console.warn(`Error converting list item child ${child.type}:`, error);
            // Fallback to text
            if (child.value) {
              children.push(LexicalUtils.createTextNode(child.value));
            }
          }
        } else {
          // Fallback: convert to text or paragraph
          if (child.type === 'text' && child.value) {
            children.push(LexicalUtils.createTextNode(child.value));
          } else if (child.type === 'paragraph' && child.children) {
            // Convert paragraph children to text nodes
            for (const grandchild of child.children) {
              if (grandchild.value) {
                children.push(LexicalUtils.createTextNode(grandchild.value));
              }
            }
          }
        }
      }
    }

    // If no children, create empty text node
    if (children.length === 0) {
      children.push(LexicalUtils.createTextNode(''));
    }

    return LexicalUtils.createListItemNode(children, node.checked);
  }

  /**
   * Convert Lexical list to Markdown
   * @private
   */
  private convertLexicalListToMarkdown(
    node: LexicalListNode,
    context?: ConversionContext,
    depth: number = 0
  ): string {
    if (!node.children || node.children.length === 0) {
      return '';
    }

    const lines: string[] = [];
    const indent = '  '.repeat(depth);
    const isOrdered = node.listType === 'number';
    let counter = node.start || 1;

    for (const child of node.children) {
      if (child.type === 'listitem') {
        const itemContent = this.convertLexicalListItemToMarkdown(
          child as LexicalListItemNode,
          context,
          depth
        );

        if (itemContent) {
          const marker = isOrdered ? `${counter}.` : '-';
          const prefix = `${indent}${marker} `;
          
          // Handle multi-line content
          const contentLines = itemContent.split('\n');
          const firstLine = contentLines[0] || '';
          lines.push(prefix + firstLine);

          // Add subsequent lines with proper indentation
          for (let i = 1; i < contentLines.length; i++) {
            const line = contentLines[i];
            if (line.trim()) {
              lines.push(indent + '  ' + line);
            } else {
              lines.push('');
            }
          }

          if (isOrdered) {
            counter++;
          }
        }
      }
    }

    return lines.join('\n');
  }

  /**
   * Convert Lexical list item to Markdown
   * @private
   */
  private convertLexicalListItemToMarkdown(
    node: LexicalListItemNode,
    context?: ConversionContext,
    depth: number = 0
  ): string {
    if (!node.children || node.children.length === 0) {
      return '';
    }

    const parts: string[] = [];

    for (const child of node.children) {
      const converter = ConverterRegistry.getConverter(child.type);
      if (converter) {
        try {
          const converted = converter.lexicalToMarkdown(child, context);
          if (converted) {
            // Handle nested lists
            if (child.type === 'list') {
              // Add extra indentation for nested lists
              const nestedList = this.convertLexicalListToMarkdown(
                child as LexicalListNode,
                context,
                depth + 1
              );
              if (nestedList) {
                parts.push('\n' + nestedList);
              }
            } else {
              parts.push(converted);
            }
          }
        } catch (error) {
          console.warn(`Error converting list item child ${child.type}:`, error);
          // Fallback: extract text
          const text = LexicalUtils.extractText(child);
          if (text) {
            parts.push(text);
          }
        }
      } else {
        // Fallback: extract text
        const text = LexicalUtils.extractText(child);
        if (text) {
          parts.push(text);
        }
      }
    }

    let result = parts.join('').trim();

    // Handle task list items (checkboxes)
    if (typeof node.checked === 'boolean') {
      const checkbox = node.checked ? '[x]' : '[ ]';
      result = `${checkbox} ${result}`;
    }

    return result;
  }

  /**
   * Parse task list syntax from markdown
   * @param text - Text that may contain task list syntax
   * @returns Object with checked status and cleaned text
   */
  static parseTaskListSyntax(text: string): { checked?: boolean; text: string } {
    const taskMatch = text.match(/^\s*\[([ xX])\]\s*(.*)/);
    if (taskMatch) {
      const checked = taskMatch[1].toLowerCase() === 'x';
      const cleanText = taskMatch[2];
      return { checked, text: cleanText };
    }
    return { text };
  }

  /**
   * Detect if a list should be treated as a task list
   * @param items - Array of list item nodes
   * @returns True if any item has checkbox syntax
   */
  static isTaskList(items: MarkdownListItemNode[]): boolean {
    return items.some(item => {
      if (item.children && item.children.length > 0) {
        const firstChild = item.children[0];
        if (firstChild.type === 'paragraph' && firstChild.children) {
          const firstText = firstChild.children[0];
          if (firstText && firstText.type === 'text' && firstText.value) {
            return /^\s*\[([ xX])\]/.test(firstText.value);
          }
        }
      }
      return false;
    });
  }
}
