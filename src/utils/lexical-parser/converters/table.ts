/**
 * Converter for table nodes between Markdown and Lexical formats
 */

import {
  Node<PERSON>onverter,
  MarkdownNode,
  LexicalNode,
  LexicalTableNode,
  LexicalTableRowNode,
  LexicalTableCellNode,
  MarkdownTableNode,
  MarkdownTableRowNode,
  MarkdownTableCellNode,
  MarkdownTableHeadNode,
  MarkdownTableBodyNode,
  ConversionContext,
} from '../types';
import { LexicalUtils } from '../utils/lexical-utils';
import { ConverterRegistry } from '../converter-registry';

/**
 * Handles conversion of table nodes
 */
export class TableConverter extends NodeConverter {
  /**
   * Check if this converter can handle the given node type
   */
  canHandle(nodeType: string): boolean {
    return ['table', 'tableHead', 'tableBody', 'tableRow', 'tableCell'].includes(nodeType);
  }

  /**
   * Convert a Markdown table node to Lexical format
   */
  markdownToLexical(
    node: MarkdownNode,
    context?: ConversionContext
  ): LexicalNode | LexicalNode[] {
    if (node.type === 'table') {
      return this.convertTable(node as MarkdownTableNode, context);
    } else if (node.type === 'tableHead' || node.type === 'tableBody') {
      // For tableHead and tableBody, just process their children (table rows)
      return this.processTableSection(node, context);
    } else if (node.type === 'tableRow') {
      return this.convertTableRow(node as MarkdownTableRowNode, context);
    } else if (node.type === 'tableCell') {
      return this.convertTableCell(node as MarkdownTableCellNode, context);
    }

    return LexicalUtils.createTextNode('');
  }

  /**
   * Convert a Lexical table node to Markdown format
   */
  lexicalToMarkdown(
    node: LexicalNode,
    context?: ConversionContext
  ): string {
    if (node.type === 'table') {
      return this.convertLexicalTableToMarkdown(node as LexicalTableNode, context);
    } else if (node.type === 'tablerow') {
      return this.convertLexicalTableRowToMarkdown(node as LexicalTableRowNode, context);
    } else if (node.type === 'tablecell') {
      return this.convertLexicalTableCellToMarkdown(node as LexicalTableCellNode, context);
    }

    return '';
  }

  /**
   * Process table section (thead/tbody) by converting their children
   * @private
   */
  private processTableSection(node: MarkdownNode, context?: ConversionContext): LexicalNode[] {
    const results: LexicalNode[] = [];

    if (node.children) {
      for (const child of node.children) {
        const converter = ConverterRegistry.getConverter(child.type);
        if (converter) {
          const converted = converter.markdownToLexical(child, context);
          if (Array.isArray(converted)) {
            results.push(...converted);
          } else if (converted) {
            results.push(converted);
          }
        }
      }
    }

    return results;
  }

  /**
   * Convert Markdown table to Lexical table
   * @private
   */
  private convertTable(
    node: MarkdownTableNode,
    context?: ConversionContext
  ): LexicalTableNode {
    const children: LexicalTableRowNode[] = [];

    if (node.children) {
      for (const child of node.children) {
        if (child.type === 'tableRow') {
          // Direct table row child
          const converted = this.convertTableRow(child as MarkdownTableRowNode, context);
          if (converted && converted.type === 'tablerow') {
            children.push(converted as LexicalTableRowNode);
          }
        } else if (child.type === 'tableHead' || child.type === 'tableBody') {
          // Table section (thead/tbody) - process their children
          const sectionNode = child as MarkdownTableHeadNode | MarkdownTableBodyNode;
          if (sectionNode.children) {
            for (const sectionChild of sectionNode.children) {
              if (sectionChild.type === 'tableRow') {
                const converted = this.convertTableRow(sectionChild, context);
                if (converted && converted.type === 'tablerow') {
                  children.push(converted as LexicalTableRowNode);
                }
              }
            }
          }
        }
      }
    }

    return this.createLexicalTableNode(children);
  }

  /**
   * Convert Markdown table row to Lexical table row
   * @private
   */
  private convertTableRow(
    node: MarkdownTableRowNode,
    context?: ConversionContext
  ): LexicalTableRowNode {
    const children: LexicalTableCellNode[] = [];

    if (node.children) {
      for (const child of node.children) {
        if (child.type === 'tableCell') {
          const converted = this.convertTableCell(child as MarkdownTableCellNode, context);
          if (converted && converted.type === 'tablecell') {
            children.push(converted as LexicalTableCellNode);
          }
        }
      }
    }

    return this.createLexicalTableRowNode(children);
  }

  /**
   * Convert Markdown table cell to Lexical table cell
   * @private
   */
  private convertTableCell(
    node: MarkdownTableCellNode,
    context?: ConversionContext
  ): LexicalTableCellNode {
    const children: LexicalNode[] = [];

    if (node.children) {
      for (const child of node.children) {
        const converter = ConverterRegistry.getConverter(child.type);
        if (converter) {
          try {
            const converted = converter.markdownToLexical(child, context);
            if (Array.isArray(converted)) {
              children.push(...converted);
            } else if (converted) {
              children.push(converted);
            }
          } catch (error) {
            console.warn(`Error converting table cell child ${child.type}:`, error);
            // Fallback to text
            if (child.value) {
              children.push(LexicalUtils.createTextNode(child.value));
            }
          }
        } else {
          // Fallback: convert to text
          if (child.value) {
            children.push(LexicalUtils.createTextNode(child.value));
          } else if (child.type === 'text') {
            children.push(LexicalUtils.createTextNode(child.value || ''));
          }
        }
      }
    }

    // If no children, create empty text node
    if (children.length === 0) {
      children.push(LexicalUtils.createTextNode(''));
    }

    return this.createLexicalTableCellNode(children);
  }

  /**
   * Convert Lexical table to Markdown
   * @private
   */
  private convertLexicalTableToMarkdown(
    node: LexicalTableNode,
    context?: ConversionContext
  ): string {
    if (!node.children || node.children.length === 0) {
      return '';
    }

    const rows: string[] = [];
    let isFirstRow = true;

    for (const child of node.children) {
      if (child.type === 'tablerow') {
        const rowMarkdown = this.convertLexicalTableRowToMarkdown(
          child as LexicalTableRowNode,
          context
        );

        if (rowMarkdown) {
          rows.push(rowMarkdown);

          // Add separator after header row
          if (isFirstRow) {
            const cellCount = child.children?.length || 0;
            const separator = '|' + ' --- |'.repeat(cellCount);
            rows.push(separator);
            isFirstRow = false;
          }
        }
      }
    }

    return rows.join('\n');
  }

  /**
   * Convert Lexical table row to Markdown
   * @private
   */
  private convertLexicalTableRowToMarkdown(
    node: LexicalTableRowNode,
    context?: ConversionContext
  ): string {
    if (!node.children || node.children.length === 0) {
      return '';
    }

    const cells: string[] = [];

    for (const child of node.children) {
      if (child.type === 'tablecell') {
        const cellContent = this.convertLexicalTableCellToMarkdown(
          child as LexicalTableCellNode,
          context
        );
        cells.push(cellContent);
      }
    }

    return '| ' + cells.join(' | ') + ' |';
  }

  /**
   * Convert Lexical table cell to Markdown
   * @private
   */
  private convertLexicalTableCellToMarkdown(
    node: LexicalTableCellNode,
    context?: ConversionContext
  ): string {
    if (!node.children || node.children.length === 0) {
      return '';
    }

    const parts: string[] = [];

    for (const child of node.children) {
      const converter = ConverterRegistry.getConverter(child.type);
      if (converter) {
        try {
          const converted = converter.lexicalToMarkdown(child, context);
          if (converted) {
            parts.push(converted);
          }
        } catch (error) {
          console.warn(`Error converting table cell child ${child.type}:`, error);
          // Fallback: extract text
          const text = LexicalUtils.extractText(child);
          if (text) {
            parts.push(text);
          }
        }
      } else {
        // Fallback: extract text
        const text = LexicalUtils.extractText(child);
        if (text) {
          parts.push(text);
        }
      }
    }

    // Escape pipe characters in cell content
    return parts.join('').replace(/\|/g, '\\|').trim();
  }

  /**
   * Create a Lexical table node
   * @private
   */
  private createLexicalTableNode(children: LexicalTableRowNode[]): LexicalTableNode {
    return {
      type: 'table',
      children,
      direction: 'ltr',
      format: '',
      indent: 0,
      version: 1
    };
  }

  /**
   * Create a Lexical table row node
   * @private
   */
  private createLexicalTableRowNode(children: LexicalTableCellNode[]): LexicalTableRowNode {
    return {
      type: 'tablerow',
      children,
      direction: 'ltr',
      format: '',
      indent: 0,
      version: 1
    };
  }

  /**
   * Create a Lexical table cell node
   * @private
   */
  private createLexicalTableCellNode(
    children: LexicalNode[],
    headerType?: 'row' | 'column' | 'both' | null
  ): LexicalTableCellNode {
    return {
      type: 'tablecell',
      children,
      headerType,
      direction: 'ltr',
      format: '',
      indent: 0,
      version: 1
    };
  }

  /**
   * Parse markdown table from text
   * @param text - Text containing a markdown table
   * @returns Parsed table structure or null
   */
  static parseMarkdownTable(text: string): {
    headers: string[];
    alignments: ('left' | 'right' | 'center' | null)[];
    rows: string[][];
  } | null {
    const lines = text.trim().split('\n');

    if (lines.length < 2) {
      return null;
    }

    // Parse header row
    const headerLine = lines[0].trim();
    if (!headerLine.startsWith('|') || !headerLine.endsWith('|')) {
      return null;
    }

    const headers = this.parseTableRow(headerLine);

    // Parse separator row
    const separatorLine = lines[1].trim();
    if (!separatorLine.startsWith('|') || !separatorLine.endsWith('|')) {
      return null;
    }

    const alignments = this.parseTableAlignments(separatorLine);

    if (alignments.length !== headers.length) {
      return null;
    }

    // Parse data rows
    const rows: string[][] = [];
    for (let i = 2; i < lines.length; i++) {
      const line = lines[i].trim();
      if (line.startsWith('|') && line.endsWith('|')) {
        const row = this.parseTableRow(line);
        rows.push(row);
      }
    }

    return { headers, alignments, rows };
  }

  /**
   * Parse a table row from markdown
   * @private
   */
  private static parseTableRow(line: string): string[] {
    // Remove leading and trailing pipes
    const content = line.slice(1, -1);

    // Split by pipes, handling escaped pipes
    const cells: string[] = [];
    let current = '';
    let escaped = false;

    for (let i = 0; i < content.length; i++) {
      const char = content[i];

      if (escaped) {
        current += char;
        escaped = false;
      } else if (char === '\\') {
        escaped = true;
      } else if (char === '|') {
        cells.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }

    // Add the last cell
    cells.push(current.trim());

    return cells;
  }

  /**
   * Parse table alignments from separator row
   * @private
   */
  private static parseTableAlignments(line: string): ('left' | 'right' | 'center' | null)[] {
    const cells = this.parseTableRow(line);

    return cells.map(cell => {
      const trimmed = cell.trim();

      if (trimmed.startsWith(':') && trimmed.endsWith(':')) {
        return 'center';
      } else if (trimmed.endsWith(':')) {
        return 'right';
      } else if (trimmed.startsWith(':')) {
        return 'left';
      } else {
        return null;
      }
    });
  }

  /**
   * Check if text contains a valid markdown table
   * @param text - Text to check
   * @returns True if text contains a table
   */
  static isMarkdownTable(text: string): boolean {
    const lines = text.trim().split('\n');

    if (lines.length < 2) {
      return false;
    }

    // Check if first two lines look like table header and separator
    const headerLine = lines[0].trim();
    const separatorLine = lines[1].trim();

    return (
      headerLine.startsWith('|') &&
      headerLine.endsWith('|') &&
      separatorLine.startsWith('|') &&
      separatorLine.endsWith('|') &&
      /^\|[\s:|-]+\|$/.test(separatorLine)
    );
  }
}
