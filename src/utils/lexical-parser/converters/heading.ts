/**
 * Converter for heading nodes between Markdown and Lexical formats
 */

import {
  NodeConverter,
  MarkdownNode,
  LexicalNode,
  LexicalHeadingNode,
  MarkdownHeadingNode,
  ConversionContext,
} from '../types';
import { LexicalUtils } from '../utils/lexical-utils';
import { ConverterRegistry } from '../converter-registry';

/**
 * <PERSON><PERSON> conversion of heading nodes (H1-H6)
 */
export class HeadingConverter extends NodeConverter {
  /**
   * Check if this converter can handle the given node type
   */
  canHandle(nodeType: string): boolean {
    return nodeType === 'heading';
  }

  /**
   * Convert a Markdown heading node to Lexical format
   */
  markdownToLexical(
    node: MarkdownNode,
    context?: ConversionContext
  ): LexicalNode | LexicalNode[] {
    const headingNode = node as MarkdownHeadingNode;
    
    // Validate depth
    if (!headingNode.depth || headingNode.depth < 1 || headingNode.depth > 6) {
      console.warn('Invalid heading depth:', headingNode.depth);
      // Fallback to h1
      headingNode.depth = 1;
    }

    // Map depth to tag
    const tag = `h${headingNode.depth}` as 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';

    // Convert child nodes
    const children: LexicalNode[] = [];
    
    if (headingNode.children) {
      for (const child of headingNode.children) {
        const converter = ConverterRegistry.getConverter(child.type);
        if (converter) {
          try {
            const converted = converter.markdownToLexical(child, context);
            if (Array.isArray(converted)) {
              children.push(...converted);
            } else if (converted) {
              children.push(converted);
            }
          } catch (error) {
            console.warn(`Error converting child node ${child.type}:`, error);
            // Add as text fallback
            if (child.value) {
              children.push(LexicalUtils.createTextNode(child.value));
            }
          }
        } else {
          // Fallback: convert to text if no converter found
          if (child.value) {
            children.push(LexicalUtils.createTextNode(child.value));
          } else if (child.type === 'text') {
            children.push(LexicalUtils.createTextNode(child.value || ''));
          }
        }
      }
    }

    // If no children, create empty text node
    if (children.length === 0) {
      children.push(LexicalUtils.createTextNode(''));
    }

    // Merge adjacent text nodes with same formatting
    const mergedChildren = LexicalUtils.mergeAdjacentTextNodes(children);

    return LexicalUtils.createHeadingNode(tag, mergedChildren);
  }

  /**
   * Convert a Lexical heading node to Markdown format
   */
  lexicalToMarkdown(
    node: LexicalNode,
    context?: ConversionContext
  ): string {
    const headingNode = node as LexicalHeadingNode;
    
    // Validate tag
    if (!headingNode.tag || !['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(headingNode.tag)) {
      console.warn('Invalid heading tag:', headingNode.tag);
      return '';
    }

    // Extract depth from tag
    const depth = parseInt(headingNode.tag.substring(1));
    const prefix = '#'.repeat(depth);

    // Convert children to text
    const parts: string[] = [];

    if (headingNode.children) {
      for (const child of headingNode.children) {
        const converter = ConverterRegistry.getConverter(child.type);
        if (converter) {
          try {
            const converted = converter.lexicalToMarkdown(child, context);
            if (converted) {
              parts.push(converted);
            }
          } catch (error) {
            console.warn(`Error converting child node ${child.type}:`, error);
            // Fallback: extract text content
            const text = LexicalUtils.extractText(child);
            if (text) {
              parts.push(text);
            }
          }
        } else {
          // Fallback: extract text content
          const text = LexicalUtils.extractText(child);
          if (text) {
            parts.push(text);
          }
        }
      }
    }

    const headingText = parts.join('').trim();
    
    // Return empty string for empty headings
    if (!headingText) {
      return '';
    }

    return `${prefix} ${headingText}`;
  }
}
