/**
 * Converter for inline nodes between Markdown and Lexical formats
 */

import {
  NodeConverter,
  MarkdownNode,
  LexicalNode,
  ConversionContext,
} from '../types';
import { ConverterRegistry } from '../converter-registry';

/**
 * Handles conversion of inline container nodes
 * Inline nodes contain text content, links, images, and formatting
 */
export class InlineConverter extends NodeConverter {
  /**
   * Check if this converter can handle the given node type
   */
  canHandle(nodeType: string): boolean {
    return nodeType === 'inline';
  }

  /**
   * Convert a Markdown inline node to Lexical format
   */
  markdownToLexical(
    node: MarkdownNode,
    context?: ConversionContext
  ): LexicalNode | LexicalNode[] {
    const results: LexicalNode[] = [];

    // Process all children of the inline node
    if (node.children && node.children.length > 0) {
      for (const child of node.children) {
        try {
          const converter = ConverterRegistry.getConverter(child.type);
          if (converter) {
            const converted = converter.markdownToLexical(child, context);
            if (Array.isArray(converted)) {
              // Filter out empty text nodes
              const filteredNodes = converted.filter(node =>
                node.type !== 'text' || (node.text && node.text.trim().length > 0)
              );
              results.push(...filteredNodes);
            } else if (converted) {
              // Filter out empty text nodes
              if (converted.type !== 'text' || (converted.text && converted.text.trim().length > 0)) {
                results.push(converted);
              }
            }
          } else {
            console.warn(`No converter found for inline child type: ${child.type}`);
            // Fallback: create text node if there's content
            if (child.value) {
              results.push({
                type: 'text',
                text: child.value,
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                version: 1
              });
            }
          }
        } catch (error) {
          console.warn(`Error converting inline child ${child.type}:`, error);
          // Fallback: create text node if there's content
          if (child.value) {
            results.push({
              type: 'text',
              text: child.value,
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              version: 1
            });
          }
        }
      }
    } else if (node.value) {
      // If no children but has value, create a text node
      results.push({
        type: 'text',
        text: node.value,
        detail: 0,
        format: 0,
        mode: 'normal',
        style: '',
        version: 1
      });
    }

    return results;
  }

  /**
   * Convert a Lexical inline node to Markdown format
   * Note: This shouldn't typically be called as inline nodes are containers
   */
  lexicalToMarkdown(
    node: LexicalNode,
    context?: ConversionContext
  ): string {
    // This is a fallback - inline nodes should be handled by their children
    console.warn('InlineConverter.lexicalToMarkdown called - this should not happen');
    return '';
  }
}
