/**
 * Converter for bookmark cards between Markdown links and Ghost bookmark cards
 */

import {
  NodeConverter,
  MarkdownNode,
  LexicalNode,
  LexicalBookmarkNode,
  MarkdownLinkNode,
  ConversionContext,
} from '../types';

/**
 * Handles conversion of bookmark cards
 */
export class BookmarkConverter extends NodeConverter {
  /**
   * Check if this converter can handle the given node type
   */
  canHandle(nodeType: string): boolean {
    return nodeType === 'bookmark' || nodeType === 'link';
  }

  /**
   * Convert a Markdown link node to Lexical bookmark format
   */
  markdownToLexical(
    node: MarkdownNode,
    context?: ConversionContext
  ): LexicalNode | LexicalNode[] {
    const linkNode = node as MarkdownLinkNode;
    
    // Only convert to bookmark if it's a standalone link
    if (this.shouldConvertToBookmark(linkNode, context)) {
      return this.createLexicalBookmarkNode(linkNode.url, linkNode.title);
    }

    // Return null to let LinkConverter handle it
    return null;
  }

  /**
   * Convert a Lexical bookmark node to Markdown format
   */
  lexicalToMarkdown(
    node: LexicalNode,
    context?: ConversionContext
  ): string {
    const bookmarkNode = node as LexicalBookmarkNode;
    
    if (!bookmarkNode.url) {
      console.warn('Bookmark node missing URL, skipping');
      return '';
    }

    // Convert bookmark to markdown link
    const title = bookmarkNode.title || bookmarkNode.url;
    return `[${title}](${bookmarkNode.url})`;
  }

  /**
   * Determine if a link should be converted to a bookmark card
   * @private
   */
  private shouldConvertToBookmark(
    linkNode: MarkdownLinkNode,
    context?: ConversionContext
  ): boolean {
    // Don't convert to bookmark if Ghost features are disabled
    if (context && !context.enableGhostFeatures) {
      return false;
    }

    // Convert to bookmark if:
    // 1. The link text is the same as the URL (standalone link)
    // 2. The link appears to be to a webpage (not an image, document, etc.)
    // 3. The URL is external (not a relative link)

    const linkText = this.extractLinkText(linkNode);
    const url = linkNode.url;

    // Check if link text is the same as URL (or very similar)
    if (linkText === url || linkText === url.replace(/^https?:\/\//, '')) {
      return this.isWebpageURL(url);
    }

    // Check if it's a bare URL in paragraph
    if (this.isBareURL(linkText, url)) {
      return this.isWebpageURL(url);
    }

    return false;
  }

  /**
   * Extract text content from link node
   * @private
   */
  private extractLinkText(linkNode: MarkdownLinkNode): string {
    if (linkNode.children && linkNode.children.length > 0) {
      return linkNode.children
        .map(child => child.value || '')
        .join('')
        .trim();
    }
    return '';
  }

  /**
   * Check if this is a bare URL
   * @private
   */
  private isBareURL(linkText: string, url: string): boolean {
    // Remove protocol for comparison
    const cleanUrl = url.replace(/^https?:\/\//, '');
    const cleanText = linkText.replace(/^https?:\/\//, '');
    
    return cleanText === cleanUrl || linkText === url;
  }

  /**
   * Check if URL points to a webpage (not a file)
   * @private
   */
  private isWebpageURL(url: string): boolean {
    // Must be HTTP/HTTPS
    if (!/^https?:\/\//.test(url)) {
      return false;
    }

    // Exclude common file extensions
    const fileExtensions = /\.(pdf|doc|docx|xls|xlsx|ppt|pptx|zip|rar|tar|gz|jpg|jpeg|png|gif|webp|svg|mp4|mp3|avi|mov)$/i;
    if (fileExtensions.test(url)) {
      return false;
    }

    // Exclude direct file downloads
    if (url.includes('/download/') || url.includes('?download=')) {
      return false;
    }

    return true;
  }

  /**
   * Create a Lexical bookmark node
   * @private
   */
  private createLexicalBookmarkNode(
    url: string,
    title?: string,
    description?: string,
    icon?: string,
    thumbnail?: string,
    author?: string,
    publisher?: string
  ): LexicalBookmarkNode {
    const bookmarkNode: LexicalBookmarkNode = {
      type: 'bookmark',
      url: this.normalizeURL(url),
      version: 1
    };

    if (title) {
      bookmarkNode.title = title;
    }

    if (description) {
      bookmarkNode.description = description;
    }

    if (icon) {
      bookmarkNode.icon = icon;
    }

    if (thumbnail) {
      bookmarkNode.thumbnail = thumbnail;
    }

    if (author) {
      bookmarkNode.author = author;
    }

    if (publisher) {
      bookmarkNode.publisher = publisher;
    }

    return bookmarkNode;
  }

  /**
   * Normalize URL format
   * @private
   */
  private normalizeURL(url: string): string {
    // Trim whitespace
    url = url.trim();

    // Ensure protocol
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = `https://${url}`;
    }

    return url;
  }

  /**
   * Extract metadata from URL (placeholder for future implementation)
   * @param url - URL to extract metadata from
   * @returns Promise with metadata
   */
  static async extractMetadata(url: string): Promise<{
    title?: string;
    description?: string;
    icon?: string;
    thumbnail?: string;
    author?: string;
    publisher?: string;
  }> {
    // This is a placeholder implementation
    // In a real implementation, this would:
    // 1. Fetch the URL
    // 2. Parse HTML for meta tags
    // 3. Extract Open Graph data
    // 4. Extract Twitter Card data
    // 5. Extract JSON-LD data
    // 6. Return structured metadata

    try {
      const domain = new URL(url).hostname;
      return {
        title: `Page from ${domain}`,
        description: `Content from ${url}`,
        publisher: domain
      };
    } catch {
      return {};
    }
  }

  /**
   * Check if URL is a social media post
   * @param url - URL to check
   * @returns True if URL is a social media post
   */
  static isSocialMediaURL(url: string): boolean {
    const socialPatterns = [
      /^https?:\/\/(www\.)?twitter\.com\/\w+\/status\/\d+/,
      /^https?:\/\/(www\.)?x\.com\/\w+\/status\/\d+/,
      /^https?:\/\/(www\.)?facebook\.com\/.*\/posts\//,
      /^https?:\/\/(www\.)?instagram\.com\/p\//,
      /^https?:\/\/(www\.)?linkedin\.com\/posts\//,
      /^https?:\/\/(www\.)?youtube\.com\/watch\?v=/,
      /^https?:\/\/youtu\.be\//,
      /^https?:\/\/(www\.)?tiktok\.com\/@.*\/video\//,
      /^https?:\/\/(www\.)?reddit\.com\/r\/.*\/comments\//,
    ];

    return socialPatterns.some(pattern => pattern.test(url));
  }

  /**
   * Get suggested bookmark title from URL
   * @param url - URL to generate title from
   * @returns Suggested title
   */
  static generateBookmarkTitle(url: string): string {
    try {
      const urlObj = new URL(url);
      const domain = urlObj.hostname.replace(/^www\./, '');
      const path = urlObj.pathname;

      // Extract meaningful part from path
      if (path && path !== '/') {
        const pathParts = path.split('/').filter(part => part.length > 0);
        if (pathParts.length > 0) {
          const lastPart = pathParts[pathParts.length - 1];
          // Convert slug to readable title
          const readable = lastPart
            .replace(/[-_]/g, ' ')
            .replace(/\b\w/g, l => l.toUpperCase());
          return `${readable} - ${domain}`;
        }
      }

      return domain;
    } catch {
      return url;
    }
  }

  /**
   * Parse bookmark metadata from HTML
   * @param html - HTML content to parse
   * @returns Extracted metadata
   */
  static parseHTMLMetadata(html: string): {
    title?: string;
    description?: string;
    icon?: string;
    thumbnail?: string;
    author?: string;
    publisher?: string;
  } {
    const metadata: {
      title?: string;
      description?: string;
      icon?: string;
      thumbnail?: string;
      author?: string;
      publisher?: string;
    } = {};

    // Extract title
    const titleMatch = html.match(/<title[^>]*>([^<]+)<\/title>/i);
    if (titleMatch) {
      metadata.title = titleMatch[1].trim();
    }

    // Extract meta description
    const descMatch = html.match(/<meta[^>]*name=["']description["'][^>]*content=["']([^"']+)["']/i);
    if (descMatch) {
      metadata.description = descMatch[1].trim();
    }

    // Extract Open Graph data
    const ogTitle = html.match(/<meta[^>]*property=["']og:title["'][^>]*content=["']([^"']+)["']/i);
    if (ogTitle) {
      metadata.title = ogTitle[1].trim();
    }

    const ogDesc = html.match(/<meta[^>]*property=["']og:description["'][^>]*content=["']([^"']+)["']/i);
    if (ogDesc) {
      metadata.description = ogDesc[1].trim();
    }

    const ogImage = html.match(/<meta[^>]*property=["']og:image["'][^>]*content=["']([^"']+)["']/i);
    if (ogImage) {
      metadata.thumbnail = ogImage[1].trim();
    }

    const ogSiteName = html.match(/<meta[^>]*property=["']og:site_name["'][^>]*content=["']([^"']+)["']/i);
    if (ogSiteName) {
      metadata.publisher = ogSiteName[1].trim();
    }

    // Extract favicon
    const faviconMatch = html.match(/<link[^>]*rel=["'](?:icon|shortcut icon)["'][^>]*href=["']([^"']+)["']/i);
    if (faviconMatch) {
      metadata.icon = faviconMatch[1].trim();
    }

    return metadata;
  }
}
