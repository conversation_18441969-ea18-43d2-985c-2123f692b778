/**
 * Fallback converter for unknown node types
 */

import {
  NodeConverter,
  MarkdownNode,
  LexicalNode,
  ConversionContext,
} from '../types';
import { LexicalUtils } from '../utils/lexical-utils';
import { ContentConverter } from '../../content-converter';

/**
 * Edge case patterns that need special handling
 */
const EDGE_CASE_PATTERNS = {
  // Malformed markdown patterns
  UNCLOSED_CODE_BLOCK: /```[^`]*$/,
  UNCLOSED_EMPHASIS: /\*[^*]*$/,
  UNCLOSED_STRONG: /\*\*[^*]*$/,
  UNCLOSED_LINK: /\[[^\]]*$/,
  MALFORMED_IMAGE: /!\[[^\]]*\([^)]*$/,
  EXCESSIVE_NESTING: /(\*{3,}|\_{3,})/,

  // Problematic characters
  NULL_BYTES: /\0/g,
  CONTROL_CHARS: /[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g,
  MIXED_LINE_ENDINGS: /\r\n|\r/g,
  EXCESSIVE_WHITESPACE: /\n{4,}/g,

  // HTML-like content that might confuse parsers
  UNCLOSED_HTML_TAG: /<[^>]*$/,
  MALFORMED_HTML: /<[^>]*[^>]/,
};

/**
 * Handles conversion of unknown node types with fallback strategies
 */
export class FallbackConverter extends NodeConverter {
  /**
   * This converter handles all node types as a fallback
   */
  canHandle(nodeType: string): boolean {
    return true;
  }

  /**
   * Convert unknown Markdown node to Lexical format
   */
  markdownToLexical(
    node: MarkdownNode,
    context?: ConversionContext
  ): LexicalNode | LexicalNode[] {
    console.warn(`Unknown markdown node type: ${node.type}, using fallback conversion`);

    try {
      // Strategy 1: Handle known edge cases
      const edgeCaseResult = this.handleMarkdownEdgeCases(node, context);
      if (edgeCaseResult) {
        return edgeCaseResult;
      }

      // Strategy 2: Convert to markdown card (preserves original formatting)
      if (context?.preserveUnknownNodes) {
        const markdown = this.nodeToMarkdownString(node);
        const sanitizedMarkdown = this.sanitizeMarkdownContent(markdown);
        return {
          type: 'markdown',
          markdown: sanitizedMarkdown,
          version: 1
        };
      }

      // Strategy 3: Extract and sanitize text content
      const text = this.extractTextFromNode(node);
      if (text) {
        const sanitizedText = this.sanitizeTextContent(text);
        return LexicalUtils.createTextNode(sanitizedText);
      }

      // Strategy 4: Return diagnostic comment for debugging
      if (context?.preserveUnknownNodes) {
        return LexicalUtils.createTextNode(this.createDiagnosticComment(node));
      }

      // Strategy 5: Return empty text node as last resort
      return LexicalUtils.createTextNode('');

    } catch (error) {
      console.error(`Error in fallback conversion for node ${node.type}:`, error);

      // Emergency fallback: return a safe text node
      return LexicalUtils.createTextNode(`[Error converting ${node.type}]`);
    }
  }

  /**
   * Convert unknown Lexical node to Markdown format
   */
  lexicalToMarkdown(
    node: LexicalNode,
    context?: ConversionContext
  ): string {
    console.warn(`Unknown lexical node type: ${node.type}, using fallback conversion`);

    try {
      // Strategy 1: Handle known edge cases
      const edgeCaseResult = this.handleLexicalEdgeCases(node, context);
      if (edgeCaseResult !== null) {
        return edgeCaseResult;
      }

      // Strategy 2: Check if it's a markdown card
      if (node.type === 'markdown' && 'markdown' in node) {
        const markdown = node.markdown as string;
        return this.sanitizeMarkdownContent(markdown);
      }

      // Strategy 3: Use HTML fallback if enabled
      if (context?.fallbackToHTML) {
        try {
          const html = this.lexicalToHTML(node);
          if (html) {
            const markdown = ContentConverter.htmlToMarkdown(html);
            return this.sanitizeMarkdownContent(markdown);
          }
        } catch (error) {
          console.warn('HTML fallback failed:', error);
        }
      }

      // Strategy 4: Extract and sanitize text content
      const text = LexicalUtils.extractText(node);
      if (text) {
        return this.sanitizeTextContent(text);
      }

      // Strategy 5: Return diagnostic comment for debugging
      if (context?.preserveUnknownNodes) {
        return this.createDiagnosticComment(node);
      }

      // Strategy 6: Return empty string as last resort
      return '';

    } catch (error) {
      console.error(`Error in fallback conversion for lexical node ${node.type}:`, error);

      // Emergency fallback: return a safe comment
      return `<!-- Error converting ${node.type}: ${error instanceof Error ? error.message : 'Unknown error'} -->`;
    }
  }

  /**
   * Convert a Markdown node to its string representation
   * @private
   */
  private nodeToMarkdownString(node: MarkdownNode): string {
    // Handle different node types with basic markdown formatting
    switch (node.type) {
      case 'text':
        return node.value || '';

      case 'paragraph':
        if (node.children) {
          return node.children.map(child => this.nodeToMarkdownString(child)).join('');
        }
        return '';

      case 'heading':
        const depth = (node as any).depth || 1;
        const prefix = '#'.repeat(Math.min(depth, 6));
        const text = node.children
          ? node.children.map(child => this.nodeToMarkdownString(child)).join('')
          : '';
        return `${prefix} ${text}`;

      case 'strong':
        const strongText = node.children
          ? node.children.map(child => this.nodeToMarkdownString(child)).join('')
          : '';
        return `**${strongText}**`;

      case 'emphasis':
        const emText = node.children
          ? node.children.map(child => this.nodeToMarkdownString(child)).join('')
          : '';
        return `*${emText}*`;

      case 'link':
        const linkText = node.children
          ? node.children.map(child => this.nodeToMarkdownString(child)).join('')
          : '';
        const url = (node as any).url || '';
        return `[${linkText}](${url})`;

      case 'image':
        const alt = (node as any).alt || '';
        const src = (node as any).url || (node as any).src || '';
        return `![${alt}](${src})`;

      case 'code':
        return `\`${node.value || ''}\``;

      case 'codeBlock':
        const lang = (node as any).lang || '';
        const code = node.value || '';
        return `\`\`\`${lang}\n${code}\n\`\`\``;

      case 'list':
        if (node.children) {
          const isOrdered = (node as any).ordered;
          return node.children.map((child, index) => {
            const marker = isOrdered ? `${index + 1}.` : '-';
            const content = this.nodeToMarkdownString(child);
            return `${marker} ${content}`;
          }).join('\n');
        }
        return '';

      case 'listItem':
        if (node.children) {
          return node.children.map(child => this.nodeToMarkdownString(child)).join('');
        }
        return '';

      case 'blockquote':
        if (node.children) {
          const content = node.children.map(child => this.nodeToMarkdownString(child)).join('\n');
          return content.split('\n').map(line => `> ${line}`).join('\n');
        }
        return '';

      default:
        // For unknown types, try to extract any text content
        if (node.value) {
          return node.value;
        }
        if (node.children) {
          return node.children.map(child => this.nodeToMarkdownString(child)).join('');
        }
        return `<!-- ${node.type} -->`;
    }
  }

  /**
   * Extract text content from a node recursively
   * @private
   */
  private extractTextFromNode(node: MarkdownNode): string {
    if (node.value) {
      return node.value;
    }

    if (node.children) {
      return node.children
        .map(child => this.extractTextFromNode(child))
        .join('')
        .trim();
    }

    return '';
  }

  /**
   * Convert Lexical node to HTML (basic implementation)
   * @private
   */
  private lexicalToHTML(node: LexicalNode): string {
    // This is a simplified HTML conversion for fallback purposes
    switch (node.type) {
      case 'text':
        const textNode = node as any;
        let textContent = textNode.text || '';

        // Apply basic formatting
        if (textNode.format) {
          if (textNode.format & 1) textContent = `<strong>${textContent}</strong>`; // Bold
          if (textNode.format & 2) textContent = `<em>${textContent}</em>`; // Italic
          if (textNode.format & 16) textContent = `<code>${textContent}</code>`; // Code
        }

        return textContent;

      case 'paragraph':
        const paragraphNode = node as any;
        if (paragraphNode.children) {
          const content = paragraphNode.children
            .map((child: LexicalNode) => this.lexicalToHTML(child))
            .join('');
          return `<p>${content}</p>`;
        }
        return '<p></p>';

      case 'heading':
        const headingNode = node as any;
        const headingTag = headingNode.tag || 'h1';
        if (headingNode.children) {
          const content = headingNode.children
            .map((child: LexicalNode) => this.lexicalToHTML(child))
            .join('');
          return `<${headingTag}>${content}</${headingTag}>`;
        }
        return `<${headingTag}></${headingTag}>`;

      case 'link':
        const linkNode = node as any;
        const url = linkNode.url || '';
        if (linkNode.children) {
          const content = linkNode.children
            .map((child: LexicalNode) => this.lexicalToHTML(child))
            .join('');
          return `<a href="${url}">${content}</a>`;
        }
        return `<a href="${url}"></a>`;

      case 'list':
        const listNode = node as any;
        const listTag = listNode.listType === 'number' ? 'ol' : 'ul';
        if (listNode.children) {
          const items = listNode.children
            .map((child: LexicalNode) => this.lexicalToHTML(child))
            .join('');
          return `<${listTag}>${items}</${listTag}>`;
        }
        return `<${listTag}></${listTag}>`;

      case 'listitem':
        const listItemNode = node as any;
        if (listItemNode.children) {
          const content = listItemNode.children
            .map((child: LexicalNode) => this.lexicalToHTML(child))
            .join('');
          return `<li>${content}</li>`;
        }
        return '<li></li>';

      default:
        // For unknown types, extract text content
        const extractedText = LexicalUtils.extractText(node);
        return extractedText ? `<span>${extractedText}</span>` : '';
    }
  }

  /**
   * Handle edge cases for Markdown nodes
   * @private
   */
  private handleMarkdownEdgeCases(
    node: MarkdownNode,
    context?: ConversionContext
  ): LexicalNode | LexicalNode[] | null {
    // Handle malformed code blocks
    if (node.type === 'codeBlock' && node.value) {
      const code = node.value;
      if (EDGE_CASE_PATTERNS.UNCLOSED_CODE_BLOCK.test(code)) {
        // Try to fix unclosed code block
        const fixedCode = code.endsWith('```') ? code : code + '\n```';
        return {
          type: 'codeblock',
          language: (node as any).lang || '',
          code: fixedCode,
          version: 1
        };
      }
    }

    // Handle malformed emphasis/strong
    if (node.type === 'text' && node.value) {
      const text = node.value;
      if (EDGE_CASE_PATTERNS.UNCLOSED_EMPHASIS.test(text) ||
          EDGE_CASE_PATTERNS.UNCLOSED_STRONG.test(text)) {
        // Clean up malformed emphasis
        const cleanedText = text.replace(/\*+$/, '');
        return LexicalUtils.createTextNode(cleanedText);
      }
    }

    // Handle malformed links
    if (node.type === 'link' && !node.url) {
      // Convert malformed link to plain text
      const linkText = this.extractTextFromNode(node);
      return LexicalUtils.createTextNode(linkText);
    }

    // Handle malformed images
    if (node.type === 'image' && (!node.url || !node.alt)) {
      // Convert malformed image to text description
      const alt = (node as any).alt || 'Image';
      const url = (node as any).url || '';
      return LexicalUtils.createTextNode(url ? `${alt}: ${url}` : alt);
    }

    return null;
  }

  /**
   * Handle edge cases for Lexical nodes
   * @private
   */
  private handleLexicalEdgeCases(
    node: LexicalNode,
    context?: ConversionContext
  ): string | null {
    // Handle malformed text nodes
    if (node.type === 'text') {
      const textNode = node as any;
      if (!textNode.text || typeof textNode.text !== 'string') {
        return '';
      }

      // Handle excessive formatting flags
      if (textNode.format && textNode.format > 255) {
        // Reset to basic formatting
        const basicFormat = textNode.format & 31; // Keep only first 5 bits
        return LexicalUtils.formatTextToMarkdown(textNode.text, basicFormat);
      }
    }

    // Handle nodes with circular references
    try {
      JSON.stringify(node);
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('circular')) {
        return `<!-- Circular reference in ${node.type} node -->`;
      }
    }

    // Handle nodes with invalid children
    if ('children' in node && node.children) {
      const children = node.children as any[];
      if (!Array.isArray(children)) {
        return `<!-- Invalid children in ${node.type} node -->`;
      }

      // Check for null/undefined children
      const validChildren = children.filter(child => child != null);
      if (validChildren.length !== children.length) {
        // Reconstruct node with valid children only
        const cleanNode = { ...node, children: validChildren };
        return this.lexicalToHTML(cleanNode);
      }
    }

    return null;
  }

  /**
   * Sanitize markdown content
   * @private
   */
  private sanitizeMarkdownContent(markdown: string): string {
    if (!markdown || typeof markdown !== 'string') {
      return '';
    }

    return markdown
      // Remove null bytes and control characters
      .replace(EDGE_CASE_PATTERNS.NULL_BYTES, '')
      .replace(EDGE_CASE_PATTERNS.CONTROL_CHARS, '')
      // Normalize line endings
      .replace(EDGE_CASE_PATTERNS.MIXED_LINE_ENDINGS, '\n')
      // Reduce excessive whitespace
      .replace(EDGE_CASE_PATTERNS.EXCESSIVE_WHITESPACE, '\n\n\n')
      // Fix unclosed code blocks
      .replace(/```[^`]*$/, '```\nUnclosed code block\n```')
      // Fix unclosed emphasis
      .replace(/\*[^*]*$/, '')
      .replace(/\*\*[^*]*$/, '')
      // Trim excessive whitespace
      .trim();
  }

  /**
   * Sanitize text content
   * @private
   */
  private sanitizeTextContent(text: string): string {
    if (!text || typeof text !== 'string') {
      return '';
    }

    return text
      // Remove null bytes and control characters
      .replace(EDGE_CASE_PATTERNS.NULL_BYTES, '')
      .replace(EDGE_CASE_PATTERNS.CONTROL_CHARS, '')
      // Normalize line endings
      .replace(EDGE_CASE_PATTERNS.MIXED_LINE_ENDINGS, '\n')
      // Limit length to prevent memory issues
      .substring(0, 100000) // 100KB limit
      // Trim whitespace
      .trim();
  }

  /**
   * Create a diagnostic comment for unknown nodes
   * @private
   */
  private createDiagnosticComment(node: LexicalNode | MarkdownNode): string {
    try {
      const nodeInfo = {
        type: node.type,
        properties: Object.keys(node).filter(key =>
          key !== 'type' &&
          key !== 'children' &&
          key !== 'parent' // Avoid circular references
        ).slice(0, 5) // Limit properties to prevent huge comments
      };

      return `<!-- Unknown node: ${JSON.stringify(nodeInfo)} -->`;
    } catch (error) {
      return `<!-- Unknown node: ${node.type} (serialization failed) -->`;
    }
  }

  /**
   * Check if content appears to be malformed
   * @private
   */
  private isMalformedContent(content: string): boolean {
    if (!content || typeof content !== 'string') {
      return true;
    }

    // Check for various malformation patterns
    return (
      EDGE_CASE_PATTERNS.NULL_BYTES.test(content) ||
      EDGE_CASE_PATTERNS.UNCLOSED_CODE_BLOCK.test(content) ||
      EDGE_CASE_PATTERNS.UNCLOSED_HTML_TAG.test(content) ||
      content.length > 1000000 // 1MB limit
    );
  }

  /**
   * Attempt to repair malformed content
   * @private
   */
  private repairMalformedContent(content: string): string {
    if (!this.isMalformedContent(content)) {
      return content;
    }

    let repaired = content;

    // Fix unclosed code blocks
    if (EDGE_CASE_PATTERNS.UNCLOSED_CODE_BLOCK.test(repaired)) {
      repaired = repaired + '\n```';
    }

    // Fix unclosed HTML tags
    if (EDGE_CASE_PATTERNS.UNCLOSED_HTML_TAG.test(repaired)) {
      repaired = repaired + '>';
    }

    // Sanitize the content
    repaired = this.sanitizeMarkdownContent(repaired);

    return repaired;
  }
}
