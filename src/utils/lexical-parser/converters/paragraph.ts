/**
 * Converter for paragraph nodes between Markdown and Lexical formats
 */

import {
  NodeConverter,
  MarkdownNode,
  LexicalNode,
  LexicalParagraphNode,
  MarkdownParagraphNode,
  ConversionContext,
} from '../types';
import { LexicalUtils } from '../utils/lexical-utils';
import { ConverterRegistry } from '../converter-registry';

/**
 * <PERSON>les conversion of paragraph nodes
 */
export class ParagraphConverter extends NodeConverter {
  /**
   * Check if this converter can handle the given node type
   */
  canHandle(nodeType: string): boolean {
    return nodeType === 'paragraph';
  }

  /**
   * Convert a Markdown paragraph node to Lexical format
   */
  markdownToLexical(
    node: MarkdownNode,
    context?: ConversionContext
  ): LexicalNode | LexicalNode[] {
    const paragraphNode = node as MarkdownParagraphNode;
    
    // Convert child nodes
    const children: LexicalNode[] = [];
    
    if (paragraphNode.children) {
      for (const child of paragraphNode.children) {
        const converter = ConverterRegistry.getConverter(child.type);
        if (converter) {
          try {
            const converted = converter.markdownToLexical(child, context);
            if (Array.isArray(converted)) {
              children.push(...converted);
            } else if (converted) {
              children.push(converted);
            }
          } catch (error) {
            console.warn(`Error converting child node ${child.type}:`, error);
            // Add as text fallback
            if (child.value) {
              children.push(LexicalUtils.createTextNode(child.value));
            }
          }
        } else {
          // Fallback: convert to text if no converter found
          if (child.value) {
            children.push(LexicalUtils.createTextNode(child.value));
          } else if (child.type === 'text') {
            children.push(LexicalUtils.createTextNode(child.value || ''));
          }
        }
      }
    }

    // If no children, create empty paragraph
    if (children.length === 0) {
      children.push(LexicalUtils.createTextNode(''));
    }

    // Merge adjacent text nodes with same formatting
    const mergedChildren = LexicalUtils.mergeAdjacentTextNodes(children);

    return LexicalUtils.createParagraphNode(mergedChildren);
  }

  /**
   * Convert a Lexical paragraph node to Markdown format
   */
  lexicalToMarkdown(
    node: LexicalNode,
    context?: ConversionContext
  ): string {
    const paragraphNode = node as LexicalParagraphNode;
    
    if (!paragraphNode.children || paragraphNode.children.length === 0) {
      return '';
    }

    const parts: string[] = [];

    for (const child of paragraphNode.children) {
      const converter = ConverterRegistry.getConverter(child.type);
      if (converter) {
        try {
          const converted = converter.lexicalToMarkdown(child, context);
          if (converted) {
            parts.push(converted);
          }
        } catch (error) {
          console.warn(`Error converting child node ${child.type}:`, error);
          // Fallback: extract text content
          const text = LexicalUtils.extractText(child);
          if (text) {
            parts.push(text);
          }
        }
      } else {
        // Fallback: extract text content
        const text = LexicalUtils.extractText(child);
        if (text) {
          parts.push(text);
        }
      }
    }

    const result = parts.join('').trim();
    
    // Return empty string for empty paragraphs to avoid extra line breaks
    return result || '';
  }
}
