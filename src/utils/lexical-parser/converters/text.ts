/**
 * Converter for text nodes between Markdown and Lexical formats
 */

import {
  <PERSON>deConverter,
  MarkdownNode,
  LexicalNode,
  LexicalTextNode,
  MarkdownTextNode,
  ConversionContext,
  TEXT_FORMAT,
} from '../types';
import { LexicalUtils } from '../utils/lexical-utils';

/**
 * Handles conversion of text nodes with formatting
 */
export class TextConverter extends NodeConverter {
  /**
   * Check if this converter can handle the given node type
   */
  canHandle(nodeType: string): boolean {
    return ['text', 'strong', 'emphasis', 'strikethrough', 'inlineCode'].includes(nodeType);
  }

  /**
   * Convert a Markdown text node to Lexical format
   */
  markdownToLexical(
    node: MarkdownNode,
    context?: ConversionContext
  ): LexicalNode | LexicalNode[] {
    // Handle different text node types
    switch (node.type) {
      case 'text':
        return this.convertPlainText(node as MarkdownTextNode);

      case 'strong':
        return this.convertFormattedText(node, TEXT_FORMAT.BOLD);

      case 'emphasis':
        return this.convertFormattedText(node, TEXT_FORMAT.ITALIC);

      case 'strikethrough':
        return this.convertFormattedText(node, TEXT_FORMAT.STRIKETHROUGH);

      case 'inlineCode':
        return this.convertInlineCode(node);

      default:
        // Fallback to plain text
        return LexicalUtils.createTextNode(node.value || '');
    }
  }

  /**
   * Convert a Lexical text node to Markdown format
   */
  lexicalToMarkdown(
    node: LexicalNode,
    context?: ConversionContext
  ): string {
    const textNode = node as LexicalTextNode;

    if (!textNode.text) {
      return '';
    }

    // Apply formatting based on format flags
    return LexicalUtils.formatTextToMarkdown(textNode.text, textNode.format || 0);
  }

  /**
   * Convert plain text node
   * @private
   */
  private convertPlainText(node: MarkdownTextNode): LexicalTextNode {
    return LexicalUtils.createTextNode(node.value || '');
  }

  /**
   * Convert formatted text node (bold, italic, strikethrough)
   * @private
   */
  private convertFormattedText(node: MarkdownNode, format: number): LexicalNode[] {
    const children: LexicalNode[] = [];

    if (node.children) {
      // Process children and apply formatting
      for (const child of node.children) {
        if (child.type === 'text') {
          const textNode = LexicalUtils.createTextNode(child.value || '', format);
          children.push(textNode);
        } else {
          // Recursively handle nested formatting
          const childConverter = new TextConverter();
          if (childConverter.canHandle(child.type)) {
            const converted = childConverter.markdownToLexical(child);
            if (Array.isArray(converted)) {
              // Apply additional formatting to converted nodes
              for (const convertedNode of converted) {
                if (LexicalUtils.isTextNode(convertedNode)) {
                  const formattedNode = LexicalUtils.applyTextFormat(convertedNode, format);
                  children.push(formattedNode);
                } else {
                  children.push(convertedNode);
                }
              }
            } else if (converted && LexicalUtils.isTextNode(converted)) {
              const formattedNode = LexicalUtils.applyTextFormat(converted, format);
              children.push(formattedNode);
            } else if (converted) {
              children.push(converted);
            }
          } else {
            // Fallback: create text node with formatting
            children.push(LexicalUtils.createTextNode(child.value || '', format));
          }
        }
      }
    } else if (node.value) {
      // Direct value with formatting
      children.push(LexicalUtils.createTextNode(node.value, format));
    }

    return children;
  }

  /**
   * Convert inline code node
   * @private
   */
  private convertInlineCode(node: MarkdownNode): LexicalTextNode {
    return LexicalUtils.createTextNode(node.value || '', TEXT_FORMAT.CODE);
  }

  /**
   * Parse markdown text with inline formatting
   * @param text - Text that may contain markdown formatting
   * @returns Array of text nodes with appropriate formatting
   */
  static parseInlineFormatting(text: string): LexicalTextNode[] {
    const nodes: LexicalTextNode[] = [];
    let currentPos = 0;

    // Regex patterns for different formatting types with style tracking
    const patterns = [
      { regex: /\*\*([^*]+)\*\*/g, format: TEXT_FORMAT.BOLD, style: 'bold-star' },
      { regex: /__([^_]+)__/g, format: TEXT_FORMAT.BOLD, style: 'bold-underscore' },
      { regex: /\*([^*]+)\*/g, format: TEXT_FORMAT.ITALIC, style: 'italic-star' },
      { regex: /_([^_]+)_/g, format: TEXT_FORMAT.ITALIC, style: 'italic-underscore' },
      { regex: /~~([^~]+)~~/g, format: TEXT_FORMAT.STRIKETHROUGH, style: 'strikethrough' },
      { regex: /`([^`]+)`/g, format: TEXT_FORMAT.CODE, style: 'code' },
    ];

    // Find all formatting matches
    const matches: Array<{
      start: number;
      end: number;
      text: string;
      format: number;
      style: string;
    }> = [];

    for (const pattern of patterns) {
      let match;
      while ((match = pattern.regex.exec(text)) !== null) {
        matches.push({
          start: match.index,
          end: match.index + match[0].length,
          text: match[1],
          format: pattern.format,
          style: pattern.style,
        });
      }
    }

    // Sort matches by position
    matches.sort((a, b) => a.start - b.start);

    // Process text with formatting
    for (const match of matches) {
      // Add plain text before this match
      if (match.start > currentPos) {
        const plainText = text.substring(currentPos, match.start);
        if (plainText) {
          nodes.push(LexicalUtils.createTextNode(plainText));
        }
      }

      // Add formatted text with style information
      nodes.push(LexicalUtils.createTextNode(match.text, match.format, match.style));
      currentPos = match.end;
    }

    // Add remaining plain text
    if (currentPos < text.length) {
      const remainingText = text.substring(currentPos);
      if (remainingText) {
        nodes.push(LexicalUtils.createTextNode(remainingText));
      }
    }

    // If no formatting found, return single text node
    if (nodes.length === 0) {
      nodes.push(LexicalUtils.createTextNode(text));
    }

    return nodes;
  }

  /**
   * Combine multiple text nodes with different formatting into markdown
   * @param nodes - Array of text nodes
   * @returns Combined markdown string
   */
  static combineTextNodesToMarkdown(nodes: LexicalTextNode[]): string {
    return nodes.map(node => {
      return LexicalUtils.formatTextToMarkdown(node.text, node.format || 0);
    }).join('');
  }
}
