/**
 * Main exports for the Lexical-Markdown parser system
 */

// Main parser class
export { LexicalMarkdownParser } from './lexical-markdown-parser';

// Type definitions
export * from './types';

// Converter registry
export { ConverterRegistry } from './converter-registry';

// Utility classes
export { LexicalUtils } from './utils/lexical-utils';
export { MarkdownASTParser } from './utils/markdown-ast';

// Individual converters
export { ParagraphConverter } from './converters/paragraph';
export { HeadingConverter } from './converters/heading';
export { TextConverter } from './converters/text';
export { InlineConverter } from './converters/inline';
export { SoftbreakConverter } from './converters/softbreak';
export { ListConverter } from './converters/list';
export { LinkConverter } from './converters/link';
export { CodeConverter } from './converters/code';
export { ImageConverter } from './converters/image';
export { TableConverter } from './converters/table';
export { CalloutConverter } from './converters/callout';
export { BookmarkConverter } from './converters/bookmark';
export { FallbackConverter } from './converters/fallback';

// Initialize default converters
import { ConverterRegistry } from './converter-registry';
import { ParagraphConverter } from './converters/paragraph';
import { HeadingConverter } from './converters/heading';
import { TextConverter } from './converters/text';
import { InlineConverter } from './converters/inline';
import { SoftbreakConverter } from './converters/softbreak';
import { ListConverter } from './converters/list';
import { LinkConverter } from './converters/link';
import { CodeConverter } from './converters/code';
import { ImageConverter } from './converters/image';
import { TableConverter } from './converters/table';
import { CalloutConverter } from './converters/callout';
import { BookmarkConverter } from './converters/bookmark';
import { FallbackConverter } from './converters/fallback';

/**
 * Initialize the parser with default converters
 */
export function initializeLexicalParser(): void {
  // Register core converters
  ConverterRegistry.register('paragraph', new ParagraphConverter());
  ConverterRegistry.register('heading', new HeadingConverter());
  ConverterRegistry.register('text', new TextConverter());
  ConverterRegistry.register('inline', new InlineConverter());
  ConverterRegistry.register('softbreak', new SoftbreakConverter());
  ConverterRegistry.register('strong', new TextConverter());
  ConverterRegistry.register('emphasis', new TextConverter());
  ConverterRegistry.register('strikethrough', new TextConverter());
  ConverterRegistry.register('inlineCode', new TextConverter());

  // List converters
  ConverterRegistry.register('list', new ListConverter());
  ConverterRegistry.register('listItem', new ListConverter());

  // Link and media converters
  ConverterRegistry.register('link', new LinkConverter());
  ConverterRegistry.register('image', new ImageConverter());

  // Code converters
  ConverterRegistry.register('code', new CodeConverter());
  ConverterRegistry.register('codeblock', new CodeConverter());

  // Table converters
  ConverterRegistry.register('table', new TableConverter());
  ConverterRegistry.register('tableHead', new TableConverter());
  ConverterRegistry.register('tableBody', new TableConverter());
  ConverterRegistry.register('tableRow', new TableConverter());
  ConverterRegistry.register('tableCell', new TableConverter());

  // Ghost-specific converters
  ConverterRegistry.register('callout', new CalloutConverter());
  ConverterRegistry.register('blockquote', new CalloutConverter());
  ConverterRegistry.register('bookmark', new BookmarkConverter());

  // Fallback converter (handles all unknown types)
  ConverterRegistry.registerFallback(new FallbackConverter());
}

// Auto-initialize when module is imported
initializeLexicalParser();
