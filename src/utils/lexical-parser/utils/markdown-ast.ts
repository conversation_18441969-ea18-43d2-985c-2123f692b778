/**
 * Markdown AST parsing utilities using markdown-it
 */

import MarkdownIt from 'markdown-it';
import { MarkdownAST, MarkdownNode } from '../types';

/**
 * Markdown AST parser using markdown-it
 */
export class MarkdownASTParser {
  private static markdownIt: MarkdownIt;

  /**
   * Initialize markdown-it instance with plugins
   */
  private static initialize(): void {
    if (this.markdownIt) return;

    this.markdownIt = new MarkdownIt({
      html: true,        // Enable HTML tags in source
      xhtmlOut: false,   // Use '/' to close single tags (<br />)
      breaks: false,     // Convert '\n' in paragraphs into <br>
      langPrefix: 'language-',  // CSS language prefix for fenced blocks
      linkify: false,    // Disable autoconvert to avoid conflicts with explicit links
      typographer: false, // Disable typographer to preserve special characters exactly
    });

    // Add support for strikethrough and tables
    this.markdownIt.enable(['strikethrough', 'table']);
  }

  /**
   * Parse markdown string to AST
   * @param markdown - The markdown content to parse
   * @returns Parsed AST or null if parsing fails
   */
  static parse(markdown: string): MarkdownAST | null {
    this.initialize();

    try {
      // Parse markdown to tokens
      const tokens = this.markdownIt.parse(markdown, {});



      // Convert tokens to our AST format
      const children = this.convertTokensToNodes(tokens);

      return {
        type: 'root',
        children
      };
    } catch (error) {
      console.error('Failed to parse markdown:', error);
      return null;
    }
  }

  /**
   * Convert markdown-it tokens to our node format
   * @private
   */
  private static convertTokensToNodes(tokens: any[]): MarkdownNode[] {
    const nodes: MarkdownNode[] = [];
    const stack: MarkdownNode[] = [];
    let currentNode: MarkdownNode | null = null;

    for (let i = 0; i < tokens.length; i++) {
      const token = tokens[i];

      if (token.nesting === 1) {
        // Opening tag
        const node = this.createNodeFromToken(token);

        if (currentNode && currentNode.children) {
          currentNode.children.push(node);
        } else {
          nodes.push(node);
        }

        stack.push(currentNode);
        currentNode = node;

      } else if (token.nesting === -1) {
        // Closing tag
        currentNode = stack.pop() || null;

      } else {
        // Self-closing or content token
        const node = this.createNodeFromToken(token);

        // Special handling for inline tokens - they contain children
        if (token.type === 'inline' && token.children) {
          node.children = this.convertTokensToNodes(token.children);
        }

        if (currentNode && currentNode.children) {
          currentNode.children.push(node);
        } else {
          nodes.push(node);
        }
      }
    }

    return nodes;
  }

  /**
   * Create a node from a markdown-it token
   * @private
   */
  private static createNodeFromToken(token: any): MarkdownNode {
    const node: MarkdownNode = {
      type: this.mapTokenTypeToNodeType(token.type),
    };

    // Add content for text tokens
    if (token.content) {
      node.value = token.content;
    }

    // Add children array for container tokens
    if (token.nesting === 1 || this.isContainerType(token.type)) {
      node.children = [];
    }

    // Handle specific token types
    switch (token.type) {
      case 'heading_open':
        node.depth = parseInt(token.tag.substring(1)) as 1 | 2 | 3 | 4 | 5 | 6;
        break;

      case 'link_open':
        const href = token.attrGet('href');
        if (href) {
          node.url = href;
          const title = token.attrGet('title');
          if (title) {
            node.title = title;
          }
        }
        break;

      case 'image':
        node.url = token.attrGet('src') || '';
        node.alt = token.content || '';
        const imgTitle = token.attrGet('title');
        if (imgTitle) {
          node.title = imgTitle;
        }
        break;

      case 'code_block':
      case 'fence':
        node.lang = token.info || undefined;
        node.value = token.content;
        break;

      case 'list_item_open':
        // Check if it's a task list item
        if (token.attrGet('class') === 'task-list-item') {
          node.checked = token.attrGet('data-checked') === 'true';
        }
        break;

      case 'ordered_list_open':
        node.ordered = true;
        const start = token.attrGet('start');
        if (start) {
          node.start = parseInt(start);
        }
        break;

      case 'bullet_list_open':
        node.ordered = false;
        break;
    }

    // Handle inline formatting
    if (token.markup) {
      switch (token.markup) {
        case '**':
        case '__':
          node.format = 'strong';
          break;
        case '*':
        case '_':
          node.format = 'emphasis';
          break;
        case '~~':
          node.format = 'strikethrough';
          break;
        case '`':
          node.format = 'code';
          break;
      }
    }

    return node;
  }

  /**
   * Map markdown-it token types to our node types
   * @private
   */
  private static mapTokenTypeToNodeType(tokenType: string): string {
    const typeMap: Record<string, string> = {
      'paragraph_open': 'paragraph',
      'heading_open': 'heading',
      'text': 'text',
      'inline': 'inline',
      'code_inline': 'inlineCode',
      'code_block': 'code',
      'fence': 'code',
      'link_open': 'link',
      'image': 'image',
      'bullet_list_open': 'list',
      'ordered_list_open': 'list',
      'list_item_open': 'listItem',
      'blockquote_open': 'blockquote',
      'hr': 'thematicBreak',
      'table_open': 'table',
      'thead_open': 'tableHead',
      'tbody_open': 'tableBody',
      'tr_open': 'tableRow',
      'th_open': 'tableCell',
      'td_open': 'tableCell',
      'strong_open': 'strong',
      'em_open': 'emphasis',
      's_open': 'strikethrough',
    };

    return typeMap[tokenType] || tokenType.replace(/_open$|_close$/, '');
  }

  /**
   * Check if a token type represents a container
   * @private
   */
  private static isContainerType(tokenType: string): boolean {
    const containerTypes = [
      'paragraph_open',
      'heading_open',
      'inline',
      'link_open',
      'bullet_list_open',
      'ordered_list_open',
      'list_item_open',
      'blockquote_open',
      'table_open',
      'thead_open',
      'tbody_open',
      'tr_open',
      'th_open',
      'td_open',
      'strong_open',
      'em_open',
      's_open',
    ];

    return containerTypes.includes(tokenType);
  }

  /**
   * Parse Obsidian callouts from markdown
   * @param markdown - Markdown content that may contain callouts
   * @returns Modified markdown with callout nodes
   */
  static parseObsidianCallouts(markdown: string): string {
    // Regex to match Obsidian callouts: > [!type] content
    const calloutRegex = /^>\s*\[!(\w+)\]\s*(.*?)(?=\n(?!>)|$)/gms;

    return markdown.replace(calloutRegex, (match, type, content) => {
      // Convert to a special marker that we can detect later
      return `<!-- CALLOUT:${type} -->\n${content.replace(/^>\s*/gm, '')}\n<!-- /CALLOUT -->`;
    });
  }

  /**
   * Extract callout information from processed markdown
   * @param content - Content that may contain callout markers
   * @returns Callout info or null
   */
  static extractCalloutInfo(content: string): { type: string; content: string } | null {
    const calloutMatch = content.match(/<!-- CALLOUT:(\w+) -->\n(.*?)\n<!-- \/CALLOUT -->/s);
    if (calloutMatch) {
      return {
        type: calloutMatch[1],
        content: calloutMatch[2]
      };
    }
    return null;
  }
}
