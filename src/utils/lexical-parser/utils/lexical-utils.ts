/**
 * Utility functions for working with Lexical documents and nodes
 */

import {
  LexicalDocument,
  LexicalNode,
  LexicalTextNode,
  LexicalParagraphNode,
  LexicalHeadingNode,
  LexicalListNode,
  LexicalListItemNode,
  TEXT_FORMAT,
  TextFormat,
} from '../types';

/**
 * Utility class for Lexical document manipulation
 */
export class LexicalUtils {
  /**
   * Create an empty Lexical document
   */
  static createEmptyDocument(): LexicalDocument {
    return {
      root: {
        type: 'root',
        children: [],
        direction: 'ltr',
        format: '',
        indent: 0,
        version: 1
      }
    };
  }

  /**
   * Create a text node with optional formatting
   */
  static createTextNode(
    text: string,
    format?: number,
    style?: string
  ): LexicalTextNode {
    return {
      type: 'text',
      text,
      detail: 0,
      format: format || 0,
      mode: 'normal',
      style: style || '',
      version: 1
    };
  }

  /**
   * Create a paragraph node
   */
  static createParagraphNode(children: LexicalNode[] = []): LexicalParagraphNode {
    return {
      type: 'paragraph',
      children,
      direction: 'ltr',
      format: '',
      indent: 0,
      version: 1
    };
  }

  /**
   * Create a heading node
   */
  static createHeadingNode(
    tag: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6',
    children: LexicalNode[] = []
  ): LexicalHeadingNode {
    return {
      type: 'heading',
      tag,
      children,
      direction: 'ltr',
      format: '',
      indent: 0,
      version: 1
    };
  }

  /**
   * Create a list node
   */
  static createListNode(
    listType: 'bullet' | 'number',
    children: LexicalListItemNode[] = [],
    start?: number
  ): LexicalListNode {
    return {
      type: 'list',
      listType,
      tag: listType === 'bullet' ? 'ul' : 'ol',
      children,
      start,
      direction: 'ltr',
      format: '',
      indent: 0,
      version: 1
    };
  }

  /**
   * Create a list item node
   */
  static createListItemNode(
    children: LexicalNode[] = [],
    checked?: boolean
  ): LexicalListItemNode {
    return {
      type: 'listitem',
      children,
      checked,
      direction: 'ltr',
      format: '',
      indent: 0,
      version: 1
    };
  }

  /**
   * Check if a node is a text node
   */
  static isTextNode(node: LexicalNode): node is LexicalTextNode {
    return node.type === 'text';
  }

  /**
   * Check if a node is a paragraph node
   */
  static isParagraphNode(node: LexicalNode): node is LexicalParagraphNode {
    return node.type === 'paragraph';
  }

  /**
   * Check if a node is a heading node
   */
  static isHeadingNode(node: LexicalNode): node is LexicalHeadingNode {
    return node.type === 'heading';
  }

  /**
   * Check if a node is a list node
   */
  static isListNode(node: LexicalNode): node is LexicalListNode {
    return node.type === 'list';
  }

  /**
   * Check if a node is a list item node
   */
  static isListItemNode(node: LexicalNode): node is LexicalListItemNode {
    return node.type === 'listitem';
  }

  /**
   * Extract plain text from a Lexical node tree
   */
  static extractText(node: LexicalNode, visitedNodes?: Set<LexicalNode>): string {
    // Initialize visited set if not provided
    if (!visitedNodes) {
      visitedNodes = new Set();
    }

    // Check for circular references
    if (visitedNodes.has(node)) {
      return '';
    }

    // Add to visited set
    visitedNodes.add(node);

    try {
      if (this.isTextNode(node)) {
        return node.text;
      }

      if ('children' in node && Array.isArray(node.children)) {
        return node.children.map(child => this.extractText(child, visitedNodes)).join('');
      }

      return '';
    } finally {
      // Remove from visited set when done
      visitedNodes.delete(node);
    }
  }

  /**
   * Apply text formatting to a text node
   */
  static applyTextFormat(node: LexicalTextNode, format: number): LexicalTextNode {
    return {
      ...node,
      format: (node.format || 0) | format
    };
  }

  /**
   * Remove text formatting from a text node
   */
  static removeTextFormat(node: LexicalTextNode, format: number): LexicalTextNode {
    return {
      ...node,
      format: (node.format || 0) & ~format
    };
  }

  /**
   * Check if a text node has specific formatting
   */
  static hasTextFormat(node: LexicalTextNode, format: number): boolean {
    return ((node.format || 0) & format) === format;
  }

  /**
   * Convert text formatting to markdown syntax
   */
  static formatTextToMarkdown(text: string, format: number, style?: string): string {
    let result = text;

    if (format & TEXT_FORMAT.BOLD) {
      result = `**${result}**`;
    }

    if (format & TEXT_FORMAT.ITALIC) {
      result = `*${result}*`;
    }

    if (format & TEXT_FORMAT.STRIKETHROUGH) {
      result = `~~${result}~~`;
    }

    if (format & TEXT_FORMAT.CODE) {
      result = `\`${result}\``;
    }

    if (format & TEXT_FORMAT.UNDERLINE) {
      // Markdown doesn't have native underline, use HTML
      result = `<u>${result}</u>`;
    }

    if (format & TEXT_FORMAT.SUBSCRIPT) {
      result = `<sub>${result}</sub>`;
    }

    if (format & TEXT_FORMAT.SUPERSCRIPT) {
      result = `<sup>${result}</sup>`;
    }

    return result;
  }

  /**
   * Parse markdown formatting to text format flags
   */
  static parseMarkdownFormatting(markdown: string): { text: string; format: number } {
    let text = markdown;
    let format = 0;

    // Bold (**text** or __text__)
    if (/^\*\*(.*)\*\*$/.test(text) || /^__(.*?)__$/.test(text)) {
      format |= TEXT_FORMAT.BOLD;
      text = text.replace(/^\*\*(.*)\*\*$/, '$1').replace(/^__(.*?)__$/, '$1');
    }

    // Italic (*text* or _text_)
    if (/^\*(.*)\*$/.test(text) || /^_(.*?)_$/.test(text)) {
      format |= TEXT_FORMAT.ITALIC;
      text = text.replace(/^\*(.*)\*$/, '$1').replace(/^_(.*?)_$/, '$1');
    }

    // Strikethrough (~~text~~)
    if (/^~~(.*)~~$/.test(text)) {
      format |= TEXT_FORMAT.STRIKETHROUGH;
      text = text.replace(/^~~(.*)~~$/, '$1');
    }

    // Code (`text`)
    if (/^`(.*)`$/.test(text)) {
      format |= TEXT_FORMAT.CODE;
      text = text.replace(/^`(.*)`$/, '$1');
    }

    // HTML formatting
    if (/<u>(.*?)<\/u>/.test(text)) {
      format |= TEXT_FORMAT.UNDERLINE;
      text = text.replace(/<u>(.*?)<\/u>/g, '$1');
    }

    if (/<sub>(.*?)<\/sub>/.test(text)) {
      format |= TEXT_FORMAT.SUBSCRIPT;
      text = text.replace(/<sub>(.*?)<\/sub>/g, '$1');
    }

    if (/<sup>(.*?)<\/sup>/.test(text)) {
      format |= TEXT_FORMAT.SUPERSCRIPT;
      text = text.replace(/<sup>(.*?)<\/sup>/g, '$1');
    }

    return { text, format };
  }

  /**
   * Traverse a Lexical document and apply a function to each node
   */
  static traverse(
    node: LexicalNode | LexicalDocument,
    callback: (node: LexicalNode, parent?: LexicalNode) => void,
    parent?: LexicalNode
  ): void {
    if ('root' in node) {
      // It's a document
      this.traverse(node.root, callback);
      return;
    }

    callback(node, parent);

    if ('children' in node && Array.isArray(node.children)) {
      for (const child of node.children) {
        this.traverse(child, callback, node);
      }
    }
  }

  /**
   * Find all nodes of a specific type in a Lexical document
   */
  static findNodesByType<T extends LexicalNode>(
    doc: LexicalDocument,
    nodeType: string
  ): T[] {
    const nodes: T[] = [];

    this.traverse(doc, (node) => {
      if (node.type === nodeType) {
        nodes.push(node as T);
      }
    });

    return nodes;
  }

  /**
   * Validate a Lexical document structure
   */
  static validateDocument(doc: LexicalDocument): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!doc.root) {
      errors.push('Document must have a root node');
      return { valid: false, errors };
    }

    if (doc.root.type !== 'root') {
      errors.push('Root node must have type "root"');
    }

    if (!Array.isArray(doc.root.children)) {
      errors.push('Root node must have a children array');
      return { valid: false, errors };
    }

    // Validate each child node
    this.traverse(doc, (node, parent) => {
      if (!node.type) {
        errors.push('All nodes must have a type property');
      }

      // Validate specific node types
      if (this.isTextNode(node)) {
        if (typeof node.text !== 'string') {
          errors.push('Text nodes must have a text property');
        }
      }

      if (this.isHeadingNode(node)) {
        if (!['h1', 'h2', 'h3', 'h4', 'h5', 'h6'].includes(node.tag)) {
          errors.push('Heading nodes must have a valid tag property');
        }
      }

      if (this.isListNode(node)) {
        if (!['bullet', 'number'].includes(node.listType)) {
          errors.push('List nodes must have a valid listType property');
        }
      }
    });

    return { valid: errors.length === 0, errors };
  }

  /**
   * Clone a Lexical node deeply
   */
  static cloneNode<T extends LexicalNode>(node: T): T {
    const cloned = { ...node };

    if ('children' in cloned && Array.isArray((cloned as any).children)) {
      (cloned as any).children = (cloned as any).children.map((child: LexicalNode) => this.cloneNode(child));
    }

    return cloned;
  }

  /**
   * Merge adjacent text nodes with the same formatting
   */
  static mergeAdjacentTextNodes(nodes: LexicalNode[]): LexicalNode[] {
    const merged: LexicalNode[] = [];
    let currentTextNode: LexicalTextNode | null = null;

    for (const node of nodes) {
      if (this.isTextNode(node)) {
        if (currentTextNode &&
            currentTextNode.format === node.format &&
            currentTextNode.style === node.style) {
          // Merge with previous text node
          currentTextNode.text += node.text;
        } else {
          // Start new text node
          if (currentTextNode) {
            merged.push(currentTextNode);
          }
          currentTextNode = this.cloneNode(node);
        }
      } else {
        // Non-text node
        if (currentTextNode) {
          merged.push(currentTextNode);
          currentTextNode = null;
        }
        merged.push(node);
      }
    }

    // Add final text node if exists
    if (currentTextNode) {
      merged.push(currentTextNode);
    }

    return merged;
  }
}
