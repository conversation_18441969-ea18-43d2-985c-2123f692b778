/**
 * Tests for the Lexical-Markdown parser system
 */

import { describe, it, expect, beforeEach } from 'vitest';
import {
  LexicalMarkdownParser,
  LexicalDocument,
  LexicalNode,
  ConverterRegistry,
  initializeLexicalParser
} from '../../src/utils/lexical-parser';

describe('LexicalMarkdownParser', () => {
  beforeEach(() => {
    // Ensure parser is initialized
    initializeLexicalParser();
  });

  describe('Basic Conversion', () => {
    it('should convert simple text to lexical', () => {
      const markdown = 'Hello world';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.root.children).toHaveLength(1);
      expect(result.data?.root.children[0].type).toBe('paragraph');
    });

    it('should convert simple lexical to markdown', () => {
      const lexicalDoc: LexicalDocument = {
        root: {
          type: 'root',
          children: [{
            type: 'paragraph',
            children: [{
              type: 'text',
              text: 'Hello world',
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              version: 1
            }],
            direction: 'ltr',
            format: '',
            indent: 0,
            version: 1
          }],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = LexicalMarkdownParser.lexicalToMarkdown(lexicalDoc);

      expect(result.success).toBe(true);
      expect(result.data).toBe('Hello world');
    });

    it('should handle empty content', () => {
      const result = LexicalMarkdownParser.markdownToLexical('');

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(0);
    });
  });

  describe('Headings', () => {
    it('should convert headings correctly', () => {
      const markdown = '# Heading 1\n## Heading 2\n### Heading 3';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(3);
      expect(result.data?.root.children[0].type).toBe('heading');
      expect((result.data?.root.children[0] as any).tag).toBe('h1');
      expect((result.data?.root.children[1] as any).tag).toBe('h2');
      expect((result.data?.root.children[2] as any).tag).toBe('h3');
    });

    it('should convert lexical headings to markdown', () => {
      const lexicalDoc: LexicalDocument = {
        root: {
          type: 'root',
          children: [{
            type: 'heading',
            tag: 'h1',
            children: [{
              type: 'text',
              text: 'Main Title',
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              version: 1
            }],
            direction: 'ltr',
            format: '',
            indent: 0,
            version: 1
          }],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = LexicalMarkdownParser.lexicalToMarkdown(lexicalDoc);

      expect(result.success).toBe(true);
      expect(result.data).toBe('# Main Title');
    });
  });

  describe('Text Formatting', () => {
    it('should handle bold text', () => {
      const markdown = '**bold text**';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('text');
      expect(textNode.format & 1).toBe(1); // Bold flag
    });

    it('should handle italic text', () => {
      const markdown = '*italic text*';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('text');
      expect(textNode.format & 2).toBe(2); // Italic flag
    });

    it('should handle inline code', () => {
      const markdown = '`code text`';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('text');
      expect(textNode.format & 16).toBe(16); // Code flag
    });
  });

  describe('Lists', () => {
    it('should convert unordered lists', () => {
      const markdown = '- Item 1\n- Item 2\n- Item 3';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(1);
      expect(result.data?.root.children[0].type).toBe('list');
      expect((result.data?.root.children[0] as any).listType).toBe('bullet');
    });

    it('should convert ordered lists', () => {
      const markdown = '1. First item\n2. Second item\n3. Third item';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(1);
      expect(result.data?.root.children[0].type).toBe('list');
      expect((result.data?.root.children[0] as any).listType).toBe('number');
    });
  });

  describe('Links', () => {
    it('should convert markdown links', () => {
      const markdown = '[Link text](https://example.com)';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const linkNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(linkNode.type).toBe('link');
      expect(linkNode.url).toBe('https://example.com');
    });

    it('should convert lexical links to markdown', () => {
      const lexicalDoc: LexicalDocument = {
        root: {
          type: 'root',
          children: [{
            type: 'paragraph',
            children: [{
              type: 'link',
              url: 'https://example.com',
              children: [{
                type: 'text',
                text: 'Example Link',
                detail: 0,
                format: 0,
                mode: 'normal',
                style: '',
                version: 1
              }],
              direction: 'ltr',
              format: '',
              indent: 0,
              version: 1
            }],
            direction: 'ltr',
            format: '',
            indent: 0,
            version: 1
          }],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = LexicalMarkdownParser.lexicalToMarkdown(lexicalDoc);

      expect(result.success).toBe(true);
      expect(result.data).toBe('[Example Link](https://example.com)');
    });
  });

  describe('Code Blocks', () => {
    it('should convert fenced code blocks', () => {
      const markdown = '```javascript\nconsole.log("Hello");\n```';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(1);
      expect(result.data?.root.children[0].type).toBe('codeblock');
      expect((result.data?.root.children[0] as any).language).toBe('javascript');
    });
  });

  describe('Round-trip Conversion', () => {
    it('should preserve content through round-trip conversion', () => {
      const originalMarkdown = '# Title\n\nThis is **bold** and *italic* text.\n\n- List item 1\n- List item 2\n\n[Link](https://example.com)';

      // Markdown → Lexical
      const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      // Lexical → Markdown
      const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);

      // The result should be semantically equivalent (may have formatting differences)
      expect(markdownResult.data).toContain('# Title');
      expect(markdownResult.data).toContain('**bold**');
      expect(markdownResult.data).toContain('*italic*');
      expect(markdownResult.data).toContain('- List item 1');
      expect(markdownResult.data).toContain('[Link](https://example.com)');
    });
  });

  describe('Error Handling', () => {
    it('should handle invalid input gracefully', () => {
      const result = LexicalMarkdownParser.markdownToLexical(null as any);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle invalid lexical document', () => {
      const result = LexicalMarkdownParser.lexicalToMarkdown(null as any);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('should handle malformed lexical document', () => {
      const malformedDoc = { root: null } as any;
      const result = LexicalMarkdownParser.lexicalToMarkdown(malformedDoc);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });
  });

  describe('Images', () => {
    it('should convert markdown images to lexical', () => {
      const markdown = '![Alt text](https://example.com/image.jpg "Title")';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const imageNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(imageNode.type).toBe('image');
      expect(imageNode.src).toBe('https://example.com/image.jpg');
      expect(imageNode.alt).toBe('Alt text');
      expect(imageNode.title).toBe('Title');
    });

    it('should convert lexical images to markdown', () => {
      const lexicalDoc: LexicalDocument = {
        root: {
          type: 'root',
          children: [{
            type: 'paragraph',
            children: [{
              type: 'image',
              src: 'https://example.com/image.jpg',
              alt: 'Test image',
              title: 'Test title',
              version: 1
            }],
            direction: 'ltr',
            format: '',
            indent: 0,
            version: 1
          }],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = LexicalMarkdownParser.lexicalToMarkdown(lexicalDoc);

      expect(result.success).toBe(true);
      expect(result.data).toBe('![Test image](https://example.com/image.jpg "Test title")');
    });

    it('should handle images without title', () => {
      const markdown = '![Alt text](https://example.com/image.jpg)';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const imageNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(imageNode.type).toBe('image');
      expect(imageNode.src).toBe('https://example.com/image.jpg');
      expect(imageNode.alt).toBe('Alt text');
      expect(imageNode.title).toBeUndefined();
    });

    it('should handle images without alt text', () => {
      const markdown = '![](https://example.com/image.jpg)';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const imageNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(imageNode.type).toBe('image');
      expect(imageNode.src).toBe('https://example.com/image.jpg');
      expect(imageNode.alt).toBe('');
    });

    it('should handle invalid image URLs gracefully', () => {
      const markdown = '![Alt text]()';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      // Should fallback to text node
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('text');
      expect(textNode.text).toBe('Alt text');
    });
  });

  describe('Tables', () => {
    it('should convert markdown tables to lexical', () => {
      const markdown = `| Header 1 | Header 2 | Header 3 |
| --- | --- | --- |
| Cell 1 | Cell 2 | Cell 3 |
| Cell 4 | Cell 5 | Cell 6 |`;

      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(1);
      expect(result.data?.root.children[0].type).toBe('table');

      const tableNode = result.data?.root.children[0] as any;
      expect(tableNode.children).toHaveLength(3); // Header + 2 data rows
      expect(tableNode.children[0].type).toBe('tablerow');
    });

    it('should convert lexical tables to markdown', () => {
      const lexicalDoc: LexicalDocument = {
        root: {
          type: 'root',
          children: [{
            type: 'table',
            children: [
              {
                type: 'tablerow',
                children: [
                  {
                    type: 'tablecell',
                    children: [{ type: 'text', text: 'Header 1', detail: 0, format: 0, mode: 'normal', style: '', version: 1 }],
                    direction: 'ltr',
                    format: '',
                    indent: 0,
                    version: 1
                  },
                  {
                    type: 'tablecell',
                    children: [{ type: 'text', text: 'Header 2', detail: 0, format: 0, mode: 'normal', style: '', version: 1 }],
                    direction: 'ltr',
                    format: '',
                    indent: 0,
                    version: 1
                  }
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                version: 1
              },
              {
                type: 'tablerow',
                children: [
                  {
                    type: 'tablecell',
                    children: [{ type: 'text', text: 'Cell 1', detail: 0, format: 0, mode: 'normal', style: '', version: 1 }],
                    direction: 'ltr',
                    format: '',
                    indent: 0,
                    version: 1
                  },
                  {
                    type: 'tablecell',
                    children: [{ type: 'text', text: 'Cell 2', detail: 0, format: 0, mode: 'normal', style: '', version: 1 }],
                    direction: 'ltr',
                    format: '',
                    indent: 0,
                    version: 1
                  }
                ],
                direction: 'ltr',
                format: '',
                indent: 0,
                version: 1
              }
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            version: 1
          }],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = LexicalMarkdownParser.lexicalToMarkdown(lexicalDoc);

      expect(result.success).toBe(true);
      expect(result.data).toContain('| Header 1 | Header 2 |');
      expect(result.data).toContain('| --- | --- |');
      expect(result.data).toContain('| Cell 1 | Cell 2 |');
    });

    it('should handle tables with escaped pipes', () => {
      const markdown = `| Header 1 | Header 2 |
| --- | --- |
| Cell with \\| pipe | Normal cell |`;

      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const tableNode = result.data?.root.children[0] as any;
      const firstDataRow = tableNode.children[1];
      const firstCell = firstDataRow.children[0];
      const cellText = firstCell.children[0].text;
      expect(cellText).toBe('Cell with | pipe');
    });

    it('should handle empty table cells', () => {
      const markdown = `| Header 1 | Header 2 |
| --- | --- |
| Cell 1 |  |
|  | Cell 2 |`;

      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const tableNode = result.data?.root.children[0] as any;
      expect(tableNode.children).toHaveLength(3); // Header + 2 data rows
    });
  });

  describe('Ghost-specific Features', () => {
    it('should convert Obsidian callouts to Ghost callouts', () => {
      const markdown = '> [!note]\n> This is a note callout';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const calloutNode = result.data?.root.children[0] as any;
      expect(calloutNode.type).toBe('callout');
      expect(calloutNode.calloutType).toBe('note');
    });

    it('should convert different callout types', () => {
      const calloutTypes = ['note', 'tip', 'warning', 'danger', 'info'];

      for (const type of calloutTypes) {
        const markdown = `> [!${type}]\n> This is a ${type} callout`;
        const result = LexicalMarkdownParser.markdownToLexical(markdown);

        expect(result.success).toBe(true);
        const calloutNode = result.data?.root.children[0] as any;
        expect(calloutNode.type).toBe('callout');
        expect(calloutNode.calloutType).toBe(type);
      }
    });

    it('should convert lexical callouts to Obsidian format', () => {
      const lexicalDoc: LexicalDocument = {
        root: {
          type: 'root',
          children: [{
            type: 'callout',
            calloutType: 'warning',
            children: [{
              type: 'text',
              text: 'This is a warning message',
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              version: 1
            }],
            version: 1
          }],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = LexicalMarkdownParser.lexicalToMarkdown(lexicalDoc);

      expect(result.success).toBe(true);
      expect(result.data).toContain('> [!warning]');
      expect(result.data).toContain('> This is a warning message');
    });

    it('should handle callouts with multiple paragraphs', () => {
      const markdown = `> [!note]
> First paragraph
>
> Second paragraph`;

      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const calloutNode = result.data?.root.children[0] as any;
      expect(calloutNode.type).toBe('callout');
      expect(calloutNode.children.length).toBeGreaterThan(1);
    });

    it('should fallback to blockquote for non-callout blockquotes', () => {
      const markdown = '> This is a regular blockquote\n> without callout syntax';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const calloutNode = result.data?.root.children[0] as any;
      expect(calloutNode.type).toBe('callout');
      expect(calloutNode.calloutType).toBe('quote');
    });
  });
  describe('Advanced Text Formatting', () => {
    it('should handle strikethrough text', () => {
      const markdown = '~~strikethrough text~~';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('text');
      expect(textNode.format & 4).toBe(4); // Strikethrough flag (IS_STRIKETHROUGH = 1 << 2 = 4)
    });

    it('should handle combined formatting', () => {
      const markdown = '***bold and italic***';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const textNode = result.data?.root.children[0]?.children?.[0] as any;
      expect(textNode.type).toBe('text');
      expect(textNode.format & 1).toBe(1); // Bold flag
      expect(textNode.format & 2).toBe(2); // Italic flag
    });

    it('should handle nested formatting', () => {
      const markdown = 'Normal **bold *and italic* text** normal';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0] as any;
      expect(paragraph.children.length).toBeGreaterThan(1);
    });

    it('should handle code within other formatting', () => {
      const markdown = '**Bold with `code` inside**';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0] as any;
      expect(paragraph.children.length).toBeGreaterThan(1);
    });
  });

  describe('Complex Nested Structures', () => {
    it('should handle nested lists', () => {
      const markdown = `- Item 1
  - Nested item 1
  - Nested item 2
    - Double nested
- Item 2`;

      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(1);
      expect(result.data?.root.children[0].type).toBe('list');
    });

    it('should handle lists with different formatting', () => {
      const markdown = `1. **Bold item**
2. *Italic item*
3. \`Code item\`
4. [Link item](https://example.com)`;

      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const listNode = result.data?.root.children[0] as any;
      expect(listNode.type).toBe('list');
      expect(listNode.listType).toBe('number');
    });

    it('should handle mixed content blocks', () => {
      const markdown = `# Heading

This is a paragraph with **bold** text.

- List item 1
- List item 2

\`\`\`javascript
console.log("code block");
\`\`\`

![Image](https://example.com/image.jpg)

| Table | Header |
| --- | --- |
| Cell | Data |`;

      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(5);

      // Check that different node types are present
      const nodeTypes = result.data?.root.children.map(child => child.type);
      expect(nodeTypes).toContain('heading');
      expect(nodeTypes).toContain('paragraph');
      expect(nodeTypes).toContain('list');
      expect(nodeTypes).toContain('codeblock');
      expect(nodeTypes).toContain('table');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle malformed markdown gracefully', () => {
      const malformedMarkdown = `# Unclosed heading
**Unclosed bold
[Unclosed link(https://example.com)
\`\`\`
Unclosed code block`;

      const result = LexicalMarkdownParser.markdownToLexical(malformedMarkdown);

      expect(result.success).toBe(true);
      // Should not throw errors and produce some output
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle very long content', () => {
      const longText = 'A'.repeat(10000);
      const markdown = `# Long Content\n\n${longText}`;

      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(2);
    });

    it('should handle unicode and special characters', () => {
      const markdown = `# Unicode Test 🚀

This contains unicode: 你好世界 🌍

And special chars: @#$%^&*()_+-={}[]|\\:";'<>?,./`;

      const result = LexicalMarkdownParser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle empty and whitespace-only content', () => {
      const testCases = ['', '   ', '\n\n\n', '\t\t\t'];

      for (const testCase of testCases) {
        const result = LexicalMarkdownParser.markdownToLexical(testCase);
        expect(result.success).toBe(true);
        expect(result.data?.root.children).toHaveLength(0);
      }
    });

    it('should handle null and undefined input', () => {
      const result1 = LexicalMarkdownParser.markdownToLexical(null as any);
      expect(result1.success).toBe(false);
      expect(result1.error).toBeDefined();

      const result2 = LexicalMarkdownParser.markdownToLexical(undefined as any);
      expect(result2.success).toBe(false);
      expect(result2.error).toBeDefined();
    });

    it('should handle deeply nested structures', () => {
      let nestedMarkdown = '> Quote level 1\n';
      for (let i = 2; i <= 10; i++) {
        nestedMarkdown += `> ${'  '.repeat(i-1)}Quote level ${i}\n`;
      }

      const result = LexicalMarkdownParser.markdownToLexical(nestedMarkdown);

      expect(result.success).toBe(true);
      // Should handle deep nesting without stack overflow
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });
  });

  describe('Fallback Converter', () => {
    it('should use fallback for unknown node types', () => {
      // This test would need to inject an unknown node type
      // For now, we'll test that the fallback converter is registered
      expect(ConverterRegistry.hasConverter('unknown_type')).toBe(true);
    });

    it('should preserve unknown nodes when enabled', () => {
      const options = {
        context: {
          preserveUnknownNodes: true,
          enableGhostFeatures: true,
          fallbackToHTML: true
        }
      };

      const markdown = 'Normal text';
      const result = LexicalMarkdownParser.markdownToLexical(markdown, options);

      expect(result.success).toBe(true);
    });

    it('should handle HTML fallback when enabled', () => {
      const options = {
        context: {
          preserveUnknownNodes: false,
          enableGhostFeatures: true,
          fallbackToHTML: true
        }
      };

      const markdown = '<div>HTML content</div>';
      const result = LexicalMarkdownParser.markdownToLexical(markdown, options);

      expect(result.success).toBe(true);
    });
  });
});

describe('ConverterRegistry', () => {
  it('should register and retrieve converters', () => {
    expect(ConverterRegistry.hasConverter('paragraph')).toBe(true);
    expect(ConverterRegistry.hasConverter('heading')).toBe(true);
    expect(ConverterRegistry.hasConverter('text')).toBe(true);
    expect(ConverterRegistry.hasConverter('unknown')).toBe(true); // Fallback
  });

  it('should return registered types', () => {
    const types = ConverterRegistry.getRegisteredTypes();
    expect(types).toContain('paragraph');
    expect(types).toContain('heading');
    expect(types).toContain('text');
  });

  it('should handle converter registration and unregistration', () => {
    const initialTypes = ConverterRegistry.getRegisteredTypes();
    const initialCount = initialTypes.length;

    // This is more of a unit test for the registry itself
    expect(initialCount).toBeGreaterThan(0);
    expect(ConverterRegistry.hasConverter('paragraph')).toBe(true);
  });
});
