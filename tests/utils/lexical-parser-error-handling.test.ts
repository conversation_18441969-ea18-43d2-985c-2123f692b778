/**
 * Error handling and fallback tests for the Lexical-Markdown parser system
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { 
  LexicalMarkdownParser,
  LexicalDocument,
  initializeLexicalParser
} from '../../src/utils/lexical-parser';

describe('Lexical Parser Error Handling Tests', () => {
  beforeEach(() => {
    // Ensure parser is initialized
    initializeLexicalParser();
  });

  describe('Input Validation', () => {
    it('should handle null and undefined input gracefully', () => {
      const result1 = LexicalMarkdownParser.markdownToLexical(null as any);
      expect(result1.success).toBe(false);
      expect(result1.error).toContain('null or undefined');
      expect(result1.errorCode).toBe('NULL_INPUT');

      const result2 = LexicalMarkdownParser.markdownToLexical(undefined as any);
      expect(result2.success).toBe(false);
      expect(result2.error).toContain('null or undefined');
      expect(result2.errorCode).toBe('NULL_INPUT');
    });

    it('should handle non-string input', () => {
      const result1 = LexicalMarkdownParser.markdownToLexical(123 as any);
      expect(result1.success).toBe(false);
      expect(result1.error).toContain('must be a string');
      expect(result1.errorCode).toBe('INVALID_INPUT');

      const result2 = LexicalMarkdownParser.markdownToLexical({} as any);
      expect(result2.success).toBe(false);
      expect(result2.error).toContain('must be a string');
      expect(result2.errorCode).toBe('INVALID_INPUT');

      const result3 = LexicalMarkdownParser.markdownToLexical([] as any);
      expect(result3.success).toBe(false);
      expect(result3.error).toContain('must be a string');
      expect(result3.errorCode).toBe('INVALID_INPUT');
    });

    it('should handle extremely large documents', () => {
      // Create a document larger than 10MB
      const largeContent = 'A'.repeat(11 * 1024 * 1024);
      const result = LexicalMarkdownParser.markdownToLexical(largeContent);
      
      expect(result.success).toBe(false);
      expect(result.error).toContain('too large');
      expect(result.errorCode).toBe('DOCUMENT_TOO_LARGE');
    });

    it('should handle empty and whitespace-only content', () => {
      const testCases = ['', '   ', '\n\n\n', '\t\t\t'];
      
      for (const testCase of testCases) {
        const result = LexicalMarkdownParser.markdownToLexical(testCase);
        expect(result.success).toBe(true);
        expect(result.data?.root.children).toHaveLength(0);
        expect(result.warnings).toEqual([]);
      }
    });
  });

  describe('Lexical Document Validation', () => {
    it('should handle null and undefined lexical documents', () => {
      const result1 = LexicalMarkdownParser.lexicalToMarkdown(null as any);
      expect(result1.success).toBe(false);
      expect(result1.error).toContain('null or undefined');
      expect(result1.errorCode).toBe('NULL_INPUT');

      const result2 = LexicalMarkdownParser.lexicalToMarkdown(undefined as any);
      expect(result2.success).toBe(false);
      expect(result2.error).toContain('null or undefined');
      expect(result2.errorCode).toBe('NULL_INPUT');
    });

    it('should handle invalid lexical document structure', () => {
      const result1 = LexicalMarkdownParser.lexicalToMarkdown({} as any);
      expect(result1.success).toBe(false);
      expect(result1.error).toContain('missing root node');
      expect(result1.errorCode).toBe('MISSING_ROOT');

      const result2 = LexicalMarkdownParser.lexicalToMarkdown({ root: null } as any);
      expect(result2.success).toBe(false);
      expect(result2.error).toContain('missing root node');
      expect(result2.errorCode).toBe('MISSING_ROOT');

      const result3 = LexicalMarkdownParser.lexicalToMarkdown({ 
        root: { type: 'root', children: 'not an array' } 
      } as any);
      expect(result3.success).toBe(false);
      expect(result3.error).toContain('children must be an array');
      expect(result3.errorCode).toBe('INVALID_CHILDREN');
    });

    it('should handle malformed lexical nodes', () => {
      const malformedDoc: LexicalDocument = {
        root: {
          type: 'root',
          children: [
            null as any,
            { type: null } as any,
            { /* missing type */ } as any,
            {
              type: 'paragraph',
              children: [
                { type: 'text', text: 'Valid text' }
              ]
            }
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = LexicalMarkdownParser.lexicalToMarkdown(malformedDoc);
      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid document structure');
      expect(result.errorCode).toBe('INVALID_STRUCTURE');
    });

    it('should handle deeply nested structures', () => {
      // Create a deeply nested structure (>50 levels)
      let deeplyNested: any = { type: 'text', text: 'deep' };
      for (let i = 0; i < 60; i++) {
        deeplyNested = {
          type: 'paragraph',
          children: [deeplyNested]
        };
      }

      const deepDoc: LexicalDocument = {
        root: {
          type: 'root',
          children: [deeplyNested],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = LexicalMarkdownParser.lexicalToMarkdown(deepDoc);
      expect(result.success).toBe(false);
      expect(result.error).toContain('too deeply nested');
      expect(result.errorCode).toBe('INVALID_STRUCTURE');
    });
  });

  describe('Malformed Markdown Handling', () => {
    it('should handle markdown with null bytes and control characters', () => {
      const malformedMarkdown = 'Hello\0World\x01\x02\x03\nNormal text';
      const result = LexicalMarkdownParser.markdownToLexical(malformedMarkdown);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
      // Should have sanitized the content
    });

    it('should handle markdown with inconsistent line endings', () => {
      const mixedLineEndings = 'Line 1\r\nLine 2\rLine 3\nLine 4';
      const result = LexicalMarkdownParser.markdownToLexical(mixedLineEndings);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle markdown with excessive whitespace', () => {
      const excessiveWhitespace = 'Paragraph 1\n\n\n\n\n\n\n\nParagraph 2';
      const result = LexicalMarkdownParser.markdownToLexical(excessiveWhitespace);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should fallback to plain text for unparseable markdown', () => {
      // Create markdown that might cause parsing issues
      const problematicMarkdown = '```\nUnclosed code block\n# Heading inside code\n**Bold inside code';
      const result = LexicalMarkdownParser.markdownToLexical(problematicMarkdown);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
      // Should have some content even if parsing partially failed
    });
  });

  describe('Error Recovery', () => {
    it('should provide warnings for conversion issues', () => {
      // This test would need to inject a scenario that causes warnings
      // For now, we'll test that the warning system works
      const markdown = '# Valid Heading\n\nValid paragraph.';
      const result = LexicalMarkdownParser.markdownToLexical(markdown);
      
      expect(result.success).toBe(true);
      expect(Array.isArray(result.warnings)).toBe(true);
    });

    it('should continue processing after individual node failures', () => {
      // Create a document with mixed valid and potentially problematic content
      const mixedContent = `# Valid Heading

Valid paragraph with **bold** text.

\`\`\`javascript
console.log("valid code");
\`\`\`

Valid list:
- Item 1
- Item 2

![Valid image](https://example.com/image.jpg)`;

      const result = LexicalMarkdownParser.markdownToLexical(mixedContent);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(3);
    });

    it('should provide detailed error information', () => {
      const result = LexicalMarkdownParser.markdownToLexical(null as any);
      
      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.errorCode).toBeDefined();
      expect(typeof result.error).toBe('string');
      expect(typeof result.errorCode).toBe('string');
    });
  });

  describe('Fallback Mechanisms', () => {
    it('should use HTML fallback when enabled', () => {
      // Test with fallback enabled
      const options = {
        context: {
          preserveUnknownNodes: true,
          enableGhostFeatures: true,
          fallbackToHTML: true
        }
      };

      const markdown = 'Normal text';
      const result = LexicalMarkdownParser.markdownToLexical(markdown, options);
      
      expect(result.success).toBe(true);
    });

    it('should preserve unknown nodes when enabled', () => {
      const options = {
        context: {
          preserveUnknownNodes: true,
          enableGhostFeatures: true,
          fallbackToHTML: false
        }
      };

      const markdown = 'Normal text';
      const result = LexicalMarkdownParser.markdownToLexical(markdown, options);
      
      expect(result.success).toBe(true);
    });

    it('should handle conversion failures gracefully', () => {
      // Create a valid lexical document
      const lexicalDoc: LexicalDocument = {
        root: {
          type: 'root',
          children: [{
            type: 'paragraph',
            children: [{
              type: 'text',
              text: 'Valid text',
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              version: 1
            }]
          }],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = LexicalMarkdownParser.lexicalToMarkdown(lexicalDoc);
      
      expect(result.success).toBe(true);
      expect(result.data).toContain('Valid text');
    });
  });

  describe('Edge Cases', () => {
    it('should handle unicode and emoji content', () => {
      const unicodeMarkdown = '# Unicode Test 🚀\n\n你好世界 🌍\n\n**Bold emoji: 💪**';
      const result = LexicalMarkdownParser.markdownToLexical(unicodeMarkdown);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle special characters and symbols', () => {
      const specialChars = '# Special: @#$%^&*()_+-={}[]|\\:";\'<>?,./';
      const result = LexicalMarkdownParser.markdownToLexical(specialChars);
      
      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(0);
    });

    it('should handle empty lexical document', () => {
      const emptyDoc: LexicalDocument = {
        root: {
          type: 'root',
          children: [],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = LexicalMarkdownParser.lexicalToMarkdown(emptyDoc);
      
      expect(result.success).toBe(true);
      expect(result.data).toBe('');
    });
  });
});
