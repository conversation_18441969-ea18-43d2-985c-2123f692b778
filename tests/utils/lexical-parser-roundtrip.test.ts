/**
 * Round-trip conversion tests for the Lexical-Markdown parser system
 * Tests that ensure Markdown → Lexical → Markdown preserves content fidelity
 */

import { describe, it, expect, beforeEach } from 'vitest';
import {
  LexicalMarkdownParser,
  initializeLexicalParser
} from '../../src/utils/lexical-parser';

describe('Round-trip Conversion Tests', () => {
  beforeEach(() => {
    // Ensure parser is initialized
    initializeLexicalParser();
  });

  describe('Basic Content Preservation', () => {
    it('should preserve simple text through round-trip', () => {
      const originalMarkdown = 'This is simple text.';

      const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toBe(originalMarkdown);
    });

    it('should preserve headings through round-trip', () => {
      const testCases = [
        '# Heading 1',
        '## Heading 2',
        '### Heading 3',
        '#### Heading 4',
        '##### Heading 5',
        '###### Heading 6'
      ];

      for (const originalMarkdown of testCases) {
        const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
        expect(lexicalResult.success).toBe(true);

        const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
        expect(markdownResult.success).toBe(true);
        expect(markdownResult.data).toBe(originalMarkdown);
      }
    });

    it('should preserve text formatting through round-trip', () => {
      const testCases = [
        '**bold text**',
        '*italic text*',
        '`inline code`',
        '~~strikethrough~~',
        '***bold and italic***'
      ];

      for (const originalMarkdown of testCases) {
        const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
        expect(lexicalResult.success).toBe(true);

        const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
        expect(markdownResult.success).toBe(true);
        expect(markdownResult.data).toBe(originalMarkdown);
      }
    });

    it('should preserve links through round-trip', () => {
      const testCases = [
        '[Simple link](https://example.com)',
        '[Link with title](https://example.com "Title")',
        '[Link with spaces](https://example.com/path with spaces)',
        '[Email link](mailto:<EMAIL>)'
      ];

      for (const originalMarkdown of testCases) {
        const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
        expect(lexicalResult.success).toBe(true);

        const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
        expect(markdownResult.success).toBe(true);
        expect(markdownResult.data).toBe(originalMarkdown);
      }
    });

    it('should preserve images through round-trip', () => {
      const testCases = [
        '![Alt text](https://example.com/image.jpg)',
        '![Alt text](https://example.com/image.jpg "Title")',
        '![](https://example.com/image.jpg)',
        '![Complex alt text with symbols!@#$](https://example.com/image.jpg "Complex title with symbols!@#$")'
      ];

      for (const originalMarkdown of testCases) {
        const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
        expect(lexicalResult.success).toBe(true);

        const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
        expect(markdownResult.success).toBe(true);
        expect(markdownResult.data).toBe(originalMarkdown);
      }
    });
  });

  describe('List Preservation', () => {
    it('should preserve unordered lists through round-trip', () => {
      const originalMarkdown = `- Item 1
- Item 2
- Item 3`;

      const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toBe(originalMarkdown);
    });

    it('should preserve ordered lists through round-trip', () => {
      const originalMarkdown = `1. First item
2. Second item
3. Third item`;

      const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toBe(originalMarkdown);
    });

    it('should preserve nested lists through round-trip', () => {
      const originalMarkdown = `- Item 1
  - Nested item 1
  - Nested item 2
- Item 2
  - Another nested item`;

      const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);

      // Allow for some formatting differences but preserve structure
      expect(markdownResult.data).toContain('Item 1');
      expect(markdownResult.data).toContain('Nested item 1');
      expect(markdownResult.data).toContain('Item 2');
    });
  });

  describe('Code Block Preservation', () => {
    it('should preserve fenced code blocks through round-trip', () => {
      const originalMarkdown = '```javascript\nconsole.log("Hello, world!");\n```';

      const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toBe(originalMarkdown);
    });

    it('should preserve code blocks without language through round-trip', () => {
      const originalMarkdown = '```\nPlain code block\nwith multiple lines\n```';

      const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toBe(originalMarkdown);
    });
  });

  describe('Table Preservation', () => {
    it('should preserve simple tables through round-trip', () => {
      const originalMarkdown = `| Header 1 | Header 2 |
| --- | --- |
| Cell 1 | Cell 2 |
| Cell 3 | Cell 4 |`;

      const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);

      // Check that all content is preserved
      expect(markdownResult.data).toContain('Header 1');
      expect(markdownResult.data).toContain('Header 2');
      expect(markdownResult.data).toContain('Cell 1');
      expect(markdownResult.data).toContain('Cell 2');
      expect(markdownResult.data).toContain('Cell 3');
      expect(markdownResult.data).toContain('Cell 4');
    });

    it('should preserve tables with formatting through round-trip', () => {
      const originalMarkdown = `| **Bold** | *Italic* | \`Code\` |
| --- | --- | --- |
| Normal | [Link](https://example.com) | Text |`;

      const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);

      // Check that formatting is preserved
      expect(markdownResult.data).toContain('**Bold**');
      expect(markdownResult.data).toContain('*Italic*');
      expect(markdownResult.data).toContain('`Code`');
      expect(markdownResult.data).toContain('[Link](https://example.com)');
    });
  });

  describe('Callout Preservation', () => {
    it('should preserve Obsidian callouts through round-trip', () => {
      const originalMarkdown = `> [!note]
> This is a note callout
> with multiple lines`;

      const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);

      expect(markdownResult.data).toContain('[!note]');
      expect(markdownResult.data).toContain('This is a note callout');
      expect(markdownResult.data).toContain('with multiple lines');
    });

    it('should preserve different callout types through round-trip', () => {
      const calloutTypes = ['note', 'tip', 'warning', 'danger', 'info'];

      for (const type of calloutTypes) {
        const originalMarkdown = `> [!${type}]
> This is a ${type} callout`;

        const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
        expect(lexicalResult.success).toBe(true);

        const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
        expect(markdownResult.success).toBe(true);

        expect(markdownResult.data).toContain(`[!${type}]`);
        expect(markdownResult.data).toContain(`This is a ${type} callout`);
      }
    });
  });

  describe('Complex Document Preservation', () => {
    it('should preserve complex mixed content through round-trip', () => {
      const originalMarkdown = `# Main Title

This is a paragraph with **bold** and *italic* text, plus a [link](https://example.com).

## Subsection

Here's a list:
- Item 1 with \`inline code\`
- Item 2 with **bold text**
- Item 3

### Code Example

\`\`\`javascript
function hello() {
  console.log("Hello, world!");
}
\`\`\`

### Table

| Feature | Status | Notes |
| --- | --- | --- |
| Parser | ✅ Complete | Working well |
| Tests | 🚧 In Progress | Almost done |

> [!tip]
> This is a helpful tip about the content above.

![Example Image](https://example.com/image.jpg "Example")`;

      const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);

      // Verify key content is preserved
      expect(markdownResult.data).toContain('# Main Title');
      expect(markdownResult.data).toContain('**bold**');
      expect(markdownResult.data).toContain('*italic*');
      expect(markdownResult.data).toContain('[link](https://example.com)');
      expect(markdownResult.data).toContain('## Subsection');
      expect(markdownResult.data).toContain('- Item 1');
      expect(markdownResult.data).toContain('`inline code`');
      expect(markdownResult.data).toContain('```javascript');
      expect(markdownResult.data).toContain('function hello()');
      expect(markdownResult.data).toContain('| Feature | Status | Notes |');
      expect(markdownResult.data).toContain('[!tip]');
      expect(markdownResult.data).toContain('![Example Image](https://example.com/image.jpg "Example")');
    });
  });

  describe('Edge Case Preservation', () => {
    it('should handle empty content round-trip', () => {
      const originalMarkdown = '';

      const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toBe('');
    });

    it('should handle whitespace-only content round-trip', () => {
      const originalMarkdown = '   \n\n   ';

      const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toBe('');
    });

    it('should preserve special characters through round-trip', () => {
      const originalMarkdown = 'Text with special chars: @#$%^&*()_+-={}[]|:";\'<>?,./';

      const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toBe(originalMarkdown);
    });

    it('should preserve unicode characters through round-trip', () => {
      const originalMarkdown = 'Unicode test: 你好世界 🌍 🚀 ✨ 💻';

      const lexicalResult = LexicalMarkdownParser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toBe(originalMarkdown);
    });
  });
});
