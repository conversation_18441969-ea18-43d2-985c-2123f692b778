/**
 * Performance tests for the Lexical-Markdown parser system
 */

import { describe, it, expect, beforeEach } from 'vitest';
import {
  LexicalMarkdownParser,
  ConverterRegistry,
  initializeLexicalParser
} from '../../src/utils/lexical-parser';

describe('Lexical Parser Performance Tests', () => {
  beforeEach(() => {
    // Ensure parser is initialized
    initializeLexicalParser();

    // Reset performance metrics
    ConverterRegistry.resetMetrics();
    LexicalMarkdownParser.clearCaches();
  });

  describe('Performance Configuration', () => {
    it('should allow performance configuration', () => {
      const originalConfig = LexicalMarkdownParser.getPerformanceConfig();

      LexicalMarkdownParser.configurePerformance({
        batchSize: 500,
        useStringBuilder: false,
        largeDocumentThreshold: 25000,
        enableConverterCache: false
      });

      const newConfig = LexicalMarkdownParser.getPerformanceConfig();

      expect(newConfig.batchSize).toBe(500);
      expect(newConfig.useStringBuilder).toBe(false);
      expect(newConfig.largeDocumentThreshold).toBe(25000);
      expect(newConfig.enableConverterCache).toBe(false);

      // Restore original config
      LexicalMarkdownParser.configurePerformance(originalConfig);
    });

    it('should clear caches when caching is disabled', () => {
      // First, enable caching and do some conversions to populate cache
      LexicalMarkdownParser.configurePerformance({ enableConverterCache: true });

      const markdown = '# Test\n\nSome content';
      LexicalMarkdownParser.markdownToLexical(markdown);

      // Now disable caching - this should clear caches
      LexicalMarkdownParser.configurePerformance({ enableConverterCache: false });

      const metrics = LexicalMarkdownParser.getPerformanceMetrics();
      expect(metrics.converterCacheSize).toBe(0);
    });
  });

  describe('Large Document Performance', () => {
    it('should handle large documents efficiently', () => {
      // Create a large markdown document
      const sections = [];
      for (let i = 0; i < 100; i++) {
        sections.push(`## Section ${i + 1}

This is paragraph ${i + 1} with **bold** and *italic* text.

- List item 1
- List item 2
- List item 3

\`\`\`javascript
function example${i}() {
  console.log("Example ${i}");
}
\`\`\`

| Column 1 | Column 2 | Column 3 |
| --- | --- | --- |
| Data ${i}-1 | Data ${i}-2 | Data ${i}-3 |

![Image ${i}](https://example.com/image${i}.jpg)`);
      }

      const largeMarkdown = `# Large Document Test\n\n${sections.join('\n\n')}`;

      // Measure conversion time
      const startTime = performance.now();
      const result = LexicalMarkdownParser.markdownToLexical(largeMarkdown);
      const endTime = performance.now();

      const conversionTime = endTime - startTime;

      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(100);

      // Conversion should complete in reasonable time (less than 1 second)
      expect(conversionTime).toBeLessThan(1000);

      console.log(`Large document conversion took ${conversionTime.toFixed(2)}ms`);
    });

    it('should use string builder for large documents', () => {
      // Configure to use string builder
      LexicalMarkdownParser.configurePerformance({
        useStringBuilder: true,
        largeDocumentThreshold: 1000 // Lower threshold for testing
      });

      // Create a document that exceeds the threshold
      const sections = [];
      for (let i = 0; i < 50; i++) {
        sections.push(`## Section ${i + 1}\n\nContent for section ${i + 1}.`);
      }
      const largeMarkdown = sections.join('\n\n');

      // Convert to Lexical and back
      const lexicalResult = LexicalMarkdownParser.markdownToLexical(largeMarkdown);
      expect(lexicalResult.success).toBe(true);

      const markdownResult = LexicalMarkdownParser.lexicalToMarkdown(lexicalResult.data!);
      expect(markdownResult.success).toBe(true);
      expect(markdownResult.data).toContain('Section 1');
      expect(markdownResult.data).toContain('Section 50');
    });
  });

  describe('Converter Caching Performance', () => {
    it('should cache converter lookups for better performance', () => {
      // Enable converter caching
      LexicalMarkdownParser.configurePerformance({ enableConverterCache: true });

      const markdown = `# Heading

**Bold text** and *italic text*.

- List item 1
- List item 2

\`\`\`javascript
console.log("code");
\`\`\`

[Link](https://example.com)

![Image](https://example.com/image.jpg)`;

      // First conversion - should populate cache
      const result1 = LexicalMarkdownParser.markdownToLexical(markdown);
      expect(result1.success).toBe(true);

      // Get initial metrics
      const initialMetrics = ConverterRegistry.getMetrics();
      expect(initialMetrics.lookups).toBeGreaterThan(0);

      // Second conversion - should benefit from cache
      const result2 = LexicalMarkdownParser.markdownToLexical(markdown);
      expect(result2.success).toBe(true);

      // Check that cache was used
      const finalMetrics = ConverterRegistry.getMetrics();
      expect(finalMetrics.cacheHitRate).toBeGreaterThan(0);
      expect(finalMetrics.cacheSize).toBeGreaterThan(0);

      console.log(`Cache hit rate: ${(finalMetrics.cacheHitRate * 100).toFixed(1)}%`);
    });

    it('should show performance improvement with caching enabled vs disabled', () => {
      const markdown = `# Performance Test

This document contains various elements:

## Text Formatting
**Bold**, *italic*, \`code\`, and ~~strikethrough~~.

## Lists
- Item 1
- Item 2
- Item 3

## Code Block
\`\`\`javascript
function test() {
  return "performance";
}
\`\`\`

## Table
| Header 1 | Header 2 |
| --- | --- |
| Cell 1 | Cell 2 |

## Image
![Test](https://example.com/test.jpg)`;

      // Test with caching disabled
      LexicalMarkdownParser.configurePerformance({ enableConverterCache: false });

      const startTimeNoCache = performance.now();
      for (let i = 0; i < 10; i++) {
        LexicalMarkdownParser.markdownToLexical(markdown);
      }
      const endTimeNoCache = performance.now();
      const timeWithoutCache = endTimeNoCache - startTimeNoCache;

      // Test with caching enabled
      LexicalMarkdownParser.configurePerformance({ enableConverterCache: true });
      LexicalMarkdownParser.clearCaches(); // Clear any existing cache

      const startTimeWithCache = performance.now();
      for (let i = 0; i < 10; i++) {
        LexicalMarkdownParser.markdownToLexical(markdown);
      }
      const endTimeWithCache = performance.now();
      const timeWithCache = endTimeWithCache - startTimeWithCache;

      expect(timeWithCache).toBeLessThanOrEqual(timeWithoutCache * 1.1);
    });
  });

  describe('Memory Usage Optimization', () => {
    it('should handle repeated conversions without memory leaks', () => {
      const markdown = '# Test\n\nSome content with **formatting**.';

      // Perform many conversions to test for memory leaks
      for (let i = 0; i < 100; i++) {
        const result = LexicalMarkdownParser.markdownToLexical(markdown);
        expect(result.success).toBe(true);

        const backResult = LexicalMarkdownParser.lexicalToMarkdown(result.data!);
        expect(backResult.success).toBe(true);
      }

      // Check that caches don't grow unbounded
      const metrics = ConverterRegistry.getMetrics();
      expect(metrics.cacheSize).toBeLessThan(50); // Reasonable cache size limit
    });

    it('should clear caches when requested', () => {
      // Populate caches
      const markdown = '# Test\n\n**Bold** and *italic*.';
      LexicalMarkdownParser.markdownToLexical(markdown);

      // Verify caches have content
      let metrics = ConverterRegistry.getMetrics();
      expect(metrics.cacheSize).toBeGreaterThan(0);

      // Clear caches
      LexicalMarkdownParser.clearCaches();
      ConverterRegistry.clearCaches();

      // Verify caches are cleared
      metrics = ConverterRegistry.getMetrics();
      expect(metrics.cacheSize).toBe(0);
    });
  });

  describe('Batch Processing', () => {
    it('should use batch processing for large documents', () => {
      // Configure small batch size for testing
      LexicalMarkdownParser.configurePerformance({ batchSize: 10 });

      // Create document with many nodes
      const items = [];
      for (let i = 0; i < 25; i++) {
        items.push(`## Heading ${i}`);
        items.push(`Paragraph ${i} content.`);
      }
      const largeMarkdown = items.join('\n\n');

      const result = LexicalMarkdownParser.markdownToLexical(largeMarkdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children.length).toBeGreaterThan(20);
    });
  });
});
